# Instructions for Augment Agent: Updating Amazon Book Data

This document outlines the steps for the Augment Agent to update the star ratings, review counts, and paperback prices for all books listed on the Crayolex website.

**Objective:** Ensure the `rating`, `reviews`, and `price` fields in `src/data/books.ts` reflect the current data on the corresponding Amazon product pages for ALL books in the array.

## Important Note
**You MUST process ALL books in the books.ts file.** The file may contain more than the books you initially see. Make sure to scroll through the entire file and update every book entry.

## Step-by-Step Process

1. **Read Book Data:**
   - Use the `str-replace-editor` tool with the `view` command on `src/data/books.ts`.
   - Parse the `books` array to get the `id`, `title`, `amazonLink`, current `rating`, current `reviews`, and current `price` for each book.
   - Count the total number of books in the array to ensure you process all of them.

2. **For Each Book in the Array:**
   - Extract the Amazon URL from the `amazonLink` field.
   - Use the `web-fetch` tool to retrieve the current Amazon page content.
   - From the fetched content, extract:
     - **Current Rating:** Look for the star rating (usually in the format of X.X out of 5 stars).
     - **Current Review Count:** Find the number of ratings/reviews.
     - **Current Paperback Price:** Look specifically for the paperback price (not Kindle/eBook). The price format should be like "$XX.XX".

3. **Compare and Update:**
   - For each book, compare the scraped data with what's currently in books.ts.
   - If any value (rating, reviews, or price) differs, note it for updating.
   - Create a list of all changes needed across all books.

4. **Make Updates:**
   - Use the `str-replace-editor` tool with the `str_replace` command to update the books.ts file.
   - For each book that needs updating, replace the old values with the new ones.
   - You can batch multiple updates in a single command by including multiple entries in the `str_replace_entries` array.

5. **Verify Updates:**
   - After making changes, view the updated file to ensure all changes were applied correctly.
   - Check that the formatting of the file remains consistent.

## Example Implementation

Here's an example of how to fetch and extract data for a book:

```
// Fetch Amazon page content
<function_calls>
<invoke name="web-fetch">
<parameter name="url">https://www.amazon.com/dp/B0CKMHSMG6
