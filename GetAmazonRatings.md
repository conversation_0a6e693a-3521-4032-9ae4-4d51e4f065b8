# Instructions for Roo: Updating Amazon Book Ratings, Reviews, and Prices

This document outlines the steps for <PERSON><PERSON> (the AI assistant) to update the star ratings, review counts, and paperback prices for the books listed on the Crayolex website using the available tools.

**Objective:** Ensure the `rating`, `reviews`, and `price` fields in `src/data/books.ts` reflect the current data on the corresponding Amazon product pages.

**Tools to Use:**

*   `read_file`: To get the current book data and Amazon links.
*   `use_mcp_tool`: Specifically the `firecrawl_scrape` tool from the `mcp-server-firecrawl` server.
*   `apply_diff`: To update the `src/data/books.ts` file.

**Steps:**

1.  **Read Book Data:**
    *   Use `<read_file>` on `src/data/books.ts`.
    *   Parse the `books` array to get the `id`, `title`, `amazonLink`, current `rating`, current `reviews`, and current `price` for each book. Keep track of the line numbers for `rating`, `reviews`, and `price` for each book ID.

2.  **Iterate and Scrape:** For each book object obtained in Step 1:
    *   **Scrape Rating:**
        *   Use `<use_mcp_tool>`:
            *   `server_name`: `mcp-server-firecrawl`
            *   `tool_name`: `firecrawl_scrape`
            *   `arguments`:
                ```json
                {
                  "url": "[Current Book's amazonLink]",
                  "formats": ["extract"],
                  "extract": {
                    "schema": {
                      "type": "object",
                      "properties": {
                        "ratingText": {
                          "type": "string",
                          "description": "Extract the text content of the element containing the numerical star rating, usually a span with class 'a-icon-alt' inside the star rating container (e.g., '#averageCustomerReviews span.a-icon-alt'). The text looks like 'X.X out of 5 stars'."
                        }
                      },
                      "required": ["ratingText"]
                    }
                  }
                }
                ```
        *   Parse the numerical rating (e.g., `4.4`) from the returned `ratingText`. Handle cases where scraping might fail or return unexpected text.
    *   **Scrape Review Count:**
        *   Use `<use_mcp_tool>`:
            *   `server_name`: `mcp-server-firecrawl`
            *   `tool_name`: `firecrawl_scrape`
            *   `arguments`:
                ```json
                {
                  "url": "[Current Book's amazonLink]",
                  "formats": ["extract"],
                  "extract": {
                    "schema": {
                      "type": "object",
                      "properties": {
                        "reviewCount": {
                          "type": "number",
                          "description": "Extract the numerical customer review count from the text usually found in an element like #acrCustomerReviewText (e.g., '10 ratings' should extract 10)."
                        }
                      },
                      "required": ["reviewCount"]
                    }
                  }
                }
                ```
        *   Get the numerical `reviewCount`. Handle cases where scraping might fail.
    *   **Scrape Paperback Price:**
        *   Use `<use_mcp_tool>`:
            *   `server_name`: `mcp-server-firecrawl`
            *   `tool_name`: `firecrawl_scrape`
            *   `arguments`:
                ```json
                {
                  "url": "[Current Book's amazonLink]",
                  "formats": ["extract"],
                  "extract": {
                    "schema": {
                      "type": "object",
                      "properties": {
                        "paperbackPrice": {
                          "type": "string",
                          "description": "Extract the paperback price (not Kindle/eBook price) from the Amazon product page. Look for elements with text like 'Paperback' nearby a price display. The price format should be like '$XX.XX'."
                        }
                      },
                      "required": ["paperbackPrice"]
                    }
                  }
                }
                ```
        *   Get the `paperbackPrice` as a string with the dollar sign (e.g., "$12.99"). Handle cases where scraping might fail or if the paperback format is not available.

3.  **Compare and Update:** For each book:
    *   **Compare Rating:** Check if the scraped numerical rating differs from the current `rating` value in the data file.
    *   **Update Rating (if needed):** If different, use `<apply_diff>` on `src/data/books.ts` to replace the old rating line with the new rating value. Use the correct line number.
    *   **Compare Reviews:** Check if the scraped `reviewCount` differs from the current `reviews` value in the data file.
    *   **Update Reviews (if needed):** If different, use `<apply_diff>` on `src/data/books.ts` to replace the old reviews line with the new review count. Use the correct line number.
    *   **Compare Price:** Check if the scraped `paperbackPrice` differs from the current `price` value in the data file.
    *   **Update Price (if needed):** If different, use `<apply_diff>` on `src/data/books.ts` to replace the old price line with the new paperback price. Use the correct line number.
    *   **Wait for Confirmation:** Ensure confirmation is received after each `apply_diff` before proceeding to the next book or update.

4.  **Completion:** Once all books have been processed, report the completion and summarize the updates made.