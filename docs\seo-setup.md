# SEO Setup for Crayolex.com

This document outlines the SEO setup for the Crayolex.com Next.js website and provides instructions for submitting the sitemap to Google Search Console.

## Implemented SEO Features

1. **robots.txt**
   - Location: `/public/robots.txt`
   - Purpose: Tells search engine crawlers which pages they can or cannot request from your site
   - Contains:
     - User-agent: *
     - Allow: /
     - Sitemap: https://crayolex.com/sitemap.xml

2. **Dynamic Sitemap**
   - Endpoint: `/sitemap.xml` (redirects to `/api/sitemap`)
   - Implementation: `/src/app/api/sitemap/route.ts`
   - Updates automatically with new routes
   - Includes priority and change frequency attributes

3. **Meta Tags**
   - Added `<meta name="robots" content="index, follow" />` to the main layout
   - Ensures search engines know to index and follow links on all pages

## Submitting Sitemap to Google Search Console

1. **Sign in to Google Search Console**
   - Visit [Google Search Console](https://search.google.com/search-console)
   - Sign in with your Google account

2. **Add Your Property (if not already done)**
   - Click on "Add property"
   - Enter your domain: `crayolex.com` (recommended) or URL prefix: `https://crayolex.com/`
   - Verify ownership through one of the provided methods

3. **Submit Sitemap**
   - In the left sidebar, click on "Sitemaps"
   - Enter `sitemap.xml` in the "Add a new sitemap" field
   - Click "Submit"

4. **Monitor Indexing**
   - Check the "Coverage" report to see indexing status
   - Address any errors or warnings that appear
   - Monitor the "Index" section to see how many pages are indexed

## Best Practices for Ongoing SEO

1. **Regularly Check Search Console**
   - Monitor for crawl errors
   - Check for mobile usability issues
   - Review search performance metrics

2. **Update Content Regularly**
   - Fresh content gets indexed more frequently

3. **Maintain Clean URL Structure**
   - When adding new routes, follow the existing pattern

4. **Page-Specific SEO**
   - Consider implementing dynamic meta tags for individual pages
   - For pages with specific SEO needs, customize meta descriptions

## Troubleshooting

If pages aren't being indexed:

1. Verify the sitemap submission was successful
2. Check for robots.txt errors in Search Console
3. Ensure meta robots tag is present and correct
4. Request indexing for specific URLs through Search Console