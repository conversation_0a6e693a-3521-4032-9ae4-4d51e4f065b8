# Global Rules for React/Next.js Development

## Follow Next.js Best Practices

Always use Next.js components and patterns as intended:

- Use `<Link>` from 'next/link' for internal navigation instead of regular `<a>` tags
- Properly separate client and server components with the 'use client' directive
- Follow the established project structure for consistency

## Text Content in JSX

### Use JavaScript Variables for Text Content

When including text content in React/Next.js components, use JavaScript variables and expressions with curly braces `{}` instead of writing text directly in JSX to avoid escaping quotes and apostrophes.

```jsx
// AVOID THIS:
<p className="text-base">
  Don&apos;t use this approach because you&apos;ll need to escape apostrophes and &quot;quotes&quot; which is tedious and error-prone.
</p>

// USE THIS INSTEAD:
const myText = "Use this approach to freely write text with apostrophes and 'quotes' without any escaping needed.";

<p className="text-base">
  {myText}
</p>
```

### For complex content with HTML

```jsx
// Use JSX fragments for content that needs HTML elements:
const complexContent = (
  <>
    This is a paragraph with a <strong>bold section</strong> and a <br/>
    line break. You can use "quotes" and apostrophes freely!
  </>
);

<div>{complexContent}</div>
```

### For lists of content

```jsx
// Define list items as an array and map through them:
const listItems = [
  "First item with 'quotes' and apostrophes",
  "Second item with more 'fancy' text",
  <>Complex item with <em>formatted</em> content</>
];

<ul>
  {listItems.map((item, index) => (
    <li key={index}>{item}</li>
  ))}
</ul>
```

This approach makes text maintenance easier and eliminates ESLint errors related to unescaped entities.

## API Keys and Environment Variables

### Never Hardcode API Keys

API keys and other sensitive credentials should ALWAYS be accessed via environment variables and never hardcoded directly in the source code.

```jsx
// NEVER DO THIS:
const apiKey = "sk_live_1234567890abcdefghijklmn";

// ALWAYS DO THIS:
const apiKey = process.env.NEXT_PUBLIC_API_KEY;
```

### Cloudflare Turnstile Implementation

#### Client-side Configuration
I want the turnstile to work on localhost as it would in production. Here are the steps:

#### Turnstile Setup for Localhost Testing

To ensure Turnstile works on `localhost` as it would for live visitors, use the following **dummy keys**:

- **Sitekey**: `3x00000000000000000000FF` (forces an interactive challenge, visible)
- **Secret Key**: `1x0000000000000000000000000000000AA` (always passes)

These dummy keys produce the `XXXX.DUMMY.TOKEN.XXXX` dummy response token, which allows testing in your development environment without conflicts. Note that production secret keys will reject the dummy token.

This setup emulates a production-like experience while adhering to Cloudflare's recommendations for development environments.

### Implementation Example

```tsx
import { useEffect, useRef, useState } from 'react';

const ContactForm = () => {
  const [token, setToken] = useState('');
  const turnstileRef = useRef(null);

  useEffect(() => {
    if (window.turnstile && turnstileRef.current) {
      window.turnstile.render(turnstileRef.current, {
        sitekey: process.env.NODE_ENV === 'development'
          ? '3x00000000000000000000FF' // Dummy key
          : process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY,
        callback: (t) => setToken(t)
      });
    }
  }, []);

  return (
    <form>
      {/* Form fields */}
      <div
        ref={turnstileRef}
        className="mt-4 h-[65px] w-full"
        style={{ minWidth: '300px' }}
      />
    </form>
  );
};
```

Key Requirements:
1. Always use environment variables for production keys
2. Include proper sizing (min-width: 300px)
3. Handle widget mounting/unmounting properly
4. Store the token for form submission

## Code Refactoring and Maintenance

### Clean Before New Approaches

When changing implementation approaches to solve a problem:

1. **Always revert or remove old code** completely before implementing a new approach to the same problem.
2. **Do not pile new solutions on top of old ones**, as this leads to:
   - Confusing logic paths
   - Potential conflicts between different implementations
   - Harder maintenance and debugging
   - Unnecessary code bloat

```tsx
// BAD APPROACH: Adding a new solution without removing the old one
useEffect(() => {
  // Old approach still here
  sessionStorage.setItem('lastVisitedAlbum', albumId);
  
  // New approach added on top
  window.history.replaceState({ lastAlbumId: albumId }, '');
}, [albumId]);

// GOOD APPROACH: Clean implementation of the new solution only
useEffect(() => {
  // Only the current approach is present
  window.history.replaceState({ lastAlbumId: albumId }, '');
}, [albumId]);
```



## Tailwind CSS Typography (`prose`) Styling

### Responsive Text Sizing within `prose`

When using the `@tailwindcss/typography` plugin (which provides the `prose` classes for styling blocks of HTML content like markdown), you might need to adjust the default text sizes, especially for responsiveness.

**Problem:** The default `prose` text size might be too small on larger screens.

**Solution:** Use Tailwind's responsive modifiers combined with the typography plugin's element modifiers to target specific elements within the `prose` container.

**Important:** Remember to target all relevant text elements separately. For example, if you want to change the size for both paragraphs (`<p>`) and list items (`<li>`), you need modifiers for both.

**Example:** To set the base font size to `text-base` on small screens and `text-lg` on large screens (992px and up) for both paragraphs and list items within a `prose` block:

```html
<div class="prose 
            prose-p:text-base lg:prose-p:text-lg 
            prose-li:text-base lg:prose-li:text-lg 
            dark:prose-invert max-w-none">
  {/* Your content here */}
</div>
```

**Font Sizes:**
- Ensure the `@tailwindcss/typography` plugin is installed (`npm install -D @tailwindcss/typography`) and added to your `tailwind.config.js` plugins array.
- Use `prose-p:` to target paragraphs.
- Use `prose-li:` to target list items.
- Apply responsive prefixes (like `lg:`) to the element-specific modifiers (e.g., `lg:prose-p:text-lg`).
- Adjust the breakpoints (`lg:`) and text sizes (`text-base`, `text-lg`) as needed for the design.
