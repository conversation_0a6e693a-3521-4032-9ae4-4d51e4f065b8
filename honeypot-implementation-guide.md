# Honeypot Field Implementation for Contact Forms

## Overview
This guide explains how to implement a honeypot field in a contact form to detect and block bot submissions while providing a testing interface on localhost.

## Complete Implementation

### 1. Create honeypot.module.css
```css
/* Honeypot field styling - hidden from humans but visible to bots */
.honeypot {
  position: absolute;
  left: -9999px;
  top: -9999px;
  opacity: 0;
  height: 0;
  width: 0;
  z-index: -1;
  overflow: hidden;
  /* These styles hide the field from humans but keep it accessible to bots */
}

/* Make honeypot visible on localhost for testing */
.honeypotVisible {
  position: relative !important;
  left: auto !important;
  top: auto !important;
  opacity: 1 !important;
  height: auto !important;
  width: auto !important;
  z-index: auto !important;
  overflow: visible !important;
  margin: 20px 0 !important;
  padding: 10px !important;
  border: 2px dashed red !important;
  background-color: #ffeeee !important;
}
```

### 2. Create HoneypotTester.tsx
```tsx
"use client";

import { useEffect, useState, RefObject } from 'react';

interface HoneypotTesterProps {
  honeypotRef: RefObject<HTMLInputElement>;
}

export default function HoneypotTester({ honeypotRef }: HoneypotTesterProps) {
  const [isClient, setIsClient] = useState(false);
  const [isLocalhost, setIsLocalhost] = useState(false);

  useEffect(() => {
    setIsClient(true);
    
    const hostname = window.location.hostname;
    if (hostname === 'localhost' || hostname.includes('127.0.0.1')) {
      setIsLocalhost(true);
      console.log('HoneypotTester: Localhost detected, showing testing UI');
    }
  }, []);

  // Only render on client and only on localhost
  if (!isClient || !isLocalhost) {
    return null;
  }

  return (
    <div className="w-full px-4 mb-8">
      <div style={{
        padding: '20px',
        border: '3px solid red',
        backgroundColor: '#fff0f0',
        borderRadius: '8px',
        marginBottom: '20px',
        color: '#9B1C1C' // Ensure text is dark red for better contrast
      }}>
        <h3 className="text-xl font-bold text-red-600 mb-2">🚨 HONEYPOT TESTING FIELD</h3>
        <p className="mb-4 text-red-800">This field is <strong>only visible in localhost</strong> for testing purposes.</p>
        <label className="block font-bold text-red-800 mb-2" htmlFor="honeypot-test">
          Your Favorite Color * (Honeypot field - fill to test bot detection)
        </label>
        <input 
          type="text" 
          id="honeypot-test"
          className="w-full p-2 border-2 border-red-300 rounded bg-white text-black"
          placeholder="Type anything here to trigger bot detection"
          onChange={(e) => {
            if (honeypotRef.current) {
              honeypotRef.current.value = e.target.value;
            }
          }}
        />
        <p className="mt-2 text-sm text-red-800">
          In production, this field is invisible to humans but visible to bots.
          The asterisk (*) makes bots think it&apos;s required.
        </p>
      </div>
    </div>
  );
}
```

### 3. Update Contact Form Component
```tsx
// Add these imports at the top of your Contact component file
import styles from "./honeypot.module.css";
import HoneypotTester from "./HoneypotTester";

// Add these state variables and refs to your component
const honeypotRef = useRef<HTMLInputElement>(null); // Honeypot field ref
const [botDetected, setBotDetected] = useState(false); // State to track if a bot was detected
const [isDevMode, setIsDevMode] = useState(false); // Track if we're in development mode

// Add this useEffect to detect localhost
useEffect(() => {
  // This will only run in the browser after hydration
  const hostname = window.location.hostname;
  console.log('Contact: Current hostname:', hostname);
  
  if (hostname === 'localhost' || hostname.includes('127.0.0.1')) {
    console.log('Contact: Development mode detected - honeypot field will be visible for testing');
    setIsDevMode(true);
  }
  
  // Add a class to the body to indicate client-side rendering is complete
  document.body.classList.add('client-rendered');
}, []);

// Update your form submission handler
const handleSubmit = async (e) => {
  e.preventDefault();

  const name = nameRef.current?.value ?? '';
  const email = emailRef.current?.value ?? '';
  const message = messageRef.current?.value ?? '';
  const favorite_color = honeypotRef.current?.value ?? '';

  // Check if honeypot field is filled (bot detected)
  if (favorite_color) {
    console.log('Bot detected! Honeypot field was filled.');
    setBotDetected(true);
    setSubmitError("We've detected that you're a bot. This form cannot be submitted.");
    return;
  }

  // Rest of your form validation and submission logic...

  // Include honeypot field in the request
  const response = await fetch("/api/contact", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      name,
      email,
      message,
      token: turnstileToken,
      favorite_color, // Include honeypot field in the request
    }),
  });

  // Rest of your response handling...
};

// Add these elements to your form JSX
<>
  {/* Hidden honeypot field (invisible in production) */}
  <div className={styles.honeypot}>
    <div className="mb-8">
      <label
        htmlFor="favorite_color"
        className="mb-3 block text-sm font-medium text-dark dark:text-white"
      >
        Your Favorite Color *
      </label>
      <input
        type="text"
        id="favorite_color"
        name="favorite_color"
        ref={honeypotRef}
        placeholder="Please enter your favorite color"
        className="border-stroke w-full rounded-sm border bg-[#f8f8f8] px-6 py-3 text-base text-body-color outline-none focus:border-primary dark:border-transparent dark:bg-[#2C303B] dark:text-body-color-dark dark:shadow-two dark:focus:border-primary dark:focus:shadow-none"
        tabIndex={-1}
        aria-hidden="true"
      />
    </div>
  </div>

  {/* Client-side only honeypot tester component */}
  <HoneypotTester honeypotRef={honeypotRef} />
</>

// Add this to your form status display
{botDetected ? (
  <p className="text-red-500 font-bold">
    We've detected that you're a bot. This form cannot be submitted.
  </p>
) : null}
```

### 4. Update API Route
```ts
// In your API route file (e.g., route.ts)

// Parse the request body
const body = await request.json();
const { name, email, message, token, favorite_color } = body;

// Check honeypot field - if it's filled, it's a bot
if (favorite_color) {
  const isLocalhost = request.headers.get('host')?.includes('localhost') ||
                     request.headers.get('host')?.includes('127.0.0.1');

  console.log(`API: Bot detected! Honeypot field was filled. ${isLocalhost ? '(Testing on localhost)' : ''}`);

  // Add more detailed message for localhost testing
  const errorMessage = isLocalhost
    ? "TESTING: Bot detection successful! The honeypot field was filled. In a real scenario, this form submission would be blocked."
    : "We've detected that you're a bot. This form cannot be submitted.";

  return NextResponse.json(
    { error: errorMessage },
    { status: 403 }
  );
}

// Rest of your API route logic...
```

## Common Issues and Solutions

### 1. Hydration Errors
If you encounter hydration errors, make sure:
- Don't use `typeof window !== 'undefined'` in render methods
- Move all client-side detection to useEffect hooks
- Use client-side only components for localhost testing UI

### 2. ESLint Errors with Apostrophes
If you get ESLint errors about unescaped entities:
- Use `&apos;` instead of `'` in JSX content
- Example: `We&apos;ve detected that you&apos;re a bot`

### 3. Visibility Issues on Localhost
If the testing UI doesn't show up on localhost:
- Add console logs to debug hostname detection
- Use a client component with explicit client-side detection
- Add visual indicators for development mode

## How It Works

1. The honeypot field is hidden from human users using CSS but remains accessible to bots
2. Bots that automatically fill out forms will likely fill in the hidden field
3. Both client-side and server-side validation check for the honeypot field
4. When a bot is detected, a clear error message is shown and the form submission is blocked
5. On localhost, a testing UI is shown to verify the honeypot field is working correctly

This implementation provides a robust defense against form spam while maintaining a good user experience.
