/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "cdn.sanity.io",
        port: "",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "",
      },
    ],
  },
  async rewrites() {
    return [
      {
        source: '/sitemap.xml',
        destination: '/api/sitemap',
      },
      // Add noindex headers to specific SVG files
      {
        source: '/images/logo/crayolex-logo-white.svg',
        destination: '/api/assets?path=/images/logo/crayolex-logo-white.svg',
      },
      {
        source: '/images/logo/crayolex-logo-dark.svg',
        destination: '/api/assets?path=/images/logo/crayolex-logo-dark.svg',
      },
    ];
  },
  async redirects() {
    return [
      {
        source: '/:path*', // Match any path on the host
        has: [
          {
            type: 'host',
            value: '^(www\\.)?ai\\.crayolex\\.com$', // Match ai.crayolex.com or www.ai.crayolex.com
          },
        ],
        destination: 'https://crayolex.com/ai/ai-edits', // Absolute URL
        permanent: true, // Use 308 permanent redirect
      },
    ];
  },
  experimental: {
    turbo: {
      // Add Turbopack configurations here
      // Example:
      // rules: {
      //   '*.svg': {
      //     loaders: ['@svgr/webpack'],
      //     as: '*.js',
      //   },
      // },
      // resolveAlias: {
      //   underscore: 'lodash',
      // },
    },
  },
};

module.exports = nextConfig;


// Injected content via Sentry wizard below

const { withSentryConfig } = require("@sentry/nextjs");

module.exports = withSentryConfig(
  module.exports,
  {
    // For all available options, see:
    // https://www.npmjs.com/package/@sentry/webpack-plugin#options

    org: "crayolex",
    project: "crayolex",

    // Only print logs for uploading source maps in CI
    silent: !process.env.CI,

    // For all available options, see:
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
    // This can increase your server load as well as your hosting bill.
    // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
    // side errors will fail.
    tunnelRoute: "/monitoring",

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,

    // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
    // See the following for more information:
    // https://docs.sentry.io/product/crons/
    // https://vercel.com/docs/cron-jobs
    automaticVercelMonitors: true,
  }
);
