{"name": "crayolex.com", "version": "2.1.0", "private": true, "scripts": {"dev": "set NODE_OPTIONS=--no-warnings && next dev --turbopack", "build": "set NODE_OPTIONS=--no-warnings && next build", "start": "set NODE_OPTIONS=--no-warnings && next start", "lint": "next lint"}, "dependencies": {"@marsidev/react-turnstile": "^1.1.0", "@next/third-parties": "^15.2.4", "@sentry/nextjs": "^9.10.1", "@supabase/supabase-js": "^2.49.4", "compression": "^1.8.0", "currency-converter-lt": "^1.3.1", "currency.js": "^2.0.4", "eslint-config-next": "^15.2.4", "express": "^4.21.2", "next": "^15.2.4", "next-themes": "^0.2.1", "nodemailer": "^6.10.0", "react": "^19.1.0", "react-before-after-slider-component": "^1.1.8", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-player": "^2.16.0", "react-select": "^5.10.1", "react-select-country-list": "^2.2.3", "react-youtube": "^10.1.0", "swiper": "^11.2.6", "yet-another-react-lightbox": "^3.21.8"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.8.9", "@types/nodemailer": "^6.4.17", "@types/react": "^19.0.8", "autoprefixer": "^10.4.17", "eslint": "^9.23.0", "postcss": "^8.4.35", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}, "resolutions": {"eslint": "9.23.0"}}