// No longer a client component
import Breadcrumb from "@/components/Common/Breadcrumb";
import Image from "next/image"; // Keep Image for signature
import Link from "next/link";
import AuthorImage from "@/components/About/AuthorImage"; // Import the new component

import { Metadata } from "next";

export const metadata: Metadata = {
  title: "About <PERSON><PERSON> | Crayolex",
  description: "Learn more about <PERSON><PERSON>, the author behind Crayolex.",
  // other metadata
};

const AboutPage = () => {
  // Removed state, ref, and handlers - they are now in AuthorImage component

  return (
    <>
      <Breadcrumb
        pageName="About the Author"
        description="Learn more about <PERSON><PERSON>."
      />
      <section className="pb-16">
        <div className="container">
          <div className="-mx-4 flex flex-wrap justify-center">
            <div className="w-full px-4 lg:w-8/12">
              <div>
                {/* Use the AuthorImage client component */}
                <div className="flex justify-center"> {/* Keep centering container */}
                  <AuthorImage
                    src="/images/about/about-zvi-twersky-2.webp"
                    alt="<PERSON><PERSON>"
                    width={400}
                    height={400}
                  />
                </div>
                <p className="mb-8 text-lg font-medium leading-relaxed text-body-color dark:text-body-color-dark sm:text-xl sm:leading-relaxed lg:text-lg lg:leading-relaxed xl:text-xl xl:leading-relaxed">
                  If a cat has nine lives, Zvi Twersky has the lives of about nine cats. He was hospitalized multiple times in the course of saving lives as an advanced EMT, combat medic, Iraq war medal recipient, ambulance driver, and phlebotomist during his 30 years of service. He is no stranger to danger, from suffering injuries while carrying people from burning houses to getting CO poisoning while treating victims at a carbon monoxide leak incident. From fainting while extracting soldiers from a flipped army jeep to collecting body parts at bombings in Israel. Currently, he has five children and 8.5 grandchildren, and his life-saving mission continues.
                </p>
                <p className="mb-10 text-lg font-medium leading-relaxed text-body-color dark:text-body-color-dark sm:text-xl sm:leading-relaxed lg:text-lg lg:leading-relaxed xl:text-xl xl:leading-relaxed">
                  From the author: “I come from a family of doctors. I am grateful to my entire family for their support and encouragement in helping me create the best medical puzzle books and children’s books.”
                </p>
                <div className="mb-10 flex justify-center"> {/* Container to center the signature */}
                  {/* Light mode signature */}
                  <Image
                    src="/images/about/sig-dark.webp"
                    alt="Zvi Twersky Signature"
                    width={150}
                    height={50} // Adjust height as needed based on image aspect ratio
                    className="dark:hidden"
                  />
                  {/* Dark mode signature */}
                  <Image
                    src="/images/about/sig-light.webp"
                    alt="Zvi Twersky Signature"
                    width={150}
                    height={50} // Adjust height as needed based on image aspect ratio
                    className="hidden dark:block"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default AboutPage;
