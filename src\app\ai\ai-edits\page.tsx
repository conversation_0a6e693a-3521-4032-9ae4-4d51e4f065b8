"use client";

import Image from "next/image";
import Link from "next/link";
import React, { useState, useRef, useEffect } from 'react';
import InteractiveBox from "@/components/Common/InteractiveBox"; // Import the new wrapper

// Import Before/After Slider
import ReactBeforeSliderComponent from 'react-before-after-slider-component';
import 'react-before-after-slider-component/dist/build.css';

// Import CountUp
import CountUp from 'react-countup';

// Import Swiper React components
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';

// Import Lightbox
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";

// Add custom Swiper pagination and navigation styles
const swiperStyles = `
  /* Swiper Container - Fixed height and positioning */
  .gallery-slider-wrapper {
    position: relative !important;
    height: 380px !important; /* Adjusted height after removing pagination */
    margin-bottom: 0px !important; /* Removed space for pagination */
    overflow: hidden !important;
  }

  /* Hide Swiper's built-in pagination completely */
  .swiper-pagination,
  .swiper-pagination-bullets,
  .swiper-pagination-horizontal {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
  }

  /* Navigation button positioning and styling */
  .gallery-slider-wrapper .swiper-button-prev,
  .gallery-slider-wrapper .swiper-button-next {
    top: 40% !important;
    z-index: 20 !important;
    --swiper-navigation-color: #0ea5e9 !important; /* Set arrow color */
    --swiper-navigation-size: 44px !important; /* Increase size significantly for visual thickness */
  }

  /* Removed custom ::after styles */


  /* Dark mode arrow color */
 .dark .gallery-slider-wrapper .swiper-button-prev,
 .dark .gallery-slider-wrapper .swiper-button-next {
    --swiper-navigation-color: #3b82f6 !important; /* Adjust dark mode color */
    /* Size is inherited from the light mode style */
 }

  /* Fixed-position custom pagination container styles removed */

  /* Custom pagination dot styling removed */

  /* Dark mode pagination dot colors removed */
`;

// List of gallery images
const galleryImages = [
  "001_001-R.webp", "002_040-R.webp", "003_103-R.webp", "004_101-R.webp", "005_124-R.webp",
  "006_01_071-R.webp", "006_031_984-R.webp", "006-2-975-R.webp", "007_087-R.webp", "008_061-R.webp",
  "009_002-T.webp", "010_009-T.webp", "011_013-T.webp", "012_050-R.webp", "013_056-R.webp",
  "014_073-R.webp", "015_075-R.webp", "016_094-R.webp", "017_003-T.webp", "018_105-R.webp",
  "019_117-R.webp", "020_079-R.webp", "021_119-R.webp", "022_062-R.webp", "023_048-R.webp",
  "024_084-R.webp", "025_082-R.webp", "026_051-R.webp", "027_012-R.webp", "028_039-R.webp",
  "029_044-R.webp", "030_004-T.webp", "031_049-R.webp", "032_129-R.webp", "033_069-R.webp",
  "034_074-R.webp", "035_006-R.webp", "036_122-R.webp", "037_045-R.webp", "038_116-R.webp",
  "039_066-R.webp", "040_107-R.webp", "041_098-R.webp", "042_015-R.webp", "043_032-R.webp",
  "044_096-R.webp", "045_052-R.webp", "046_006-T.webp", "047_132-R.webp", "048_014-R.webp",
  "049_021-R.webp", "050_047-R.webp", "051_095-R.webp", "052_030-R.webp", "053_067-R.webp",
  "054_065-R.webp", "055_131-R.webp", "056_126-R.webp", "057_080-R.webp", "058_027-R.webp",
  "059_022-R.webp", "060_086-R.webp", "061_125-R.webp", "062_102-R.webp", "063_055-R.webp",
  "064_009-R.webp", "065_113-R.webp", "066_008-T.webp", "067_089-R.webp", "068_043-R.webp",
  "069_072-R.webp", "070_123-R.webp", "071_130-R.webp", "072_134-R.webp", "073_077-R.webp",
  "074_057-R.webp", "075_100-R.webp", "076_001-T.webp", "077_034-R.webp", "078_099-R.webp",
  "079_054-R.webp", "080_007-T.webp", "081_012-T.webp", "082_008-R.webp", "083_120-R.webp",
  "084_010-T.webp", "085_016-T.webp", "086_026-R.webp", "087_038-R.webp", "088_118-R.webp",
  "089_085-R.webp", "090_088-R.webp", "091_093-R.webp", "092_104-R.webp", "093_110-R.webp",
  "094_112-R.webp", "095_028-R.webp", "096_013-R.webp", "097_002-R.webp", "098_106-R.webp",
  "099_005-R.webp", "100_016-R.webp", "101_003-R.webp", "102_010-R.webp", "103_007-R.webp",
  "104_011-R.webp", "105_020-R.webp", "106_014-T.webp", "107_053-R.webp", "108_017-R.webp",
  "109_018-R.webp", "110_019-R.webp", "111_023-R.webp", "112_024-R.webp", "113_025-R.webp",
  "114_029-R.webp", "115_031-R.webp", "116_033-R.webp", "117_035-R.webp", "118_036-R.webp",
  "119_037-R.webp", "120_041-R.webp", "121_046-R.webp", "122_058-R.webp", "123_060-R.webp",
  "124_063-R.webp", "125_064-R.webp", "126_068-R.webp", "127_070-R.webp", "128_076-R.webp",
  "129_078-R.webp", "130_081-R.webp", "131_083-R.webp", "132_090-R.webp", "133_091-R.webp",
  "134_092-R.webp", "135_097-R.webp", "136_108-R-v2.webp", "137_109-R.webp", "138_111-R.webp",
  "139_114-R.webp", "140_011-T.webp", "141_115-R.webp", "142_015-T.webp", "143_121-R.webp",
  "144_127-R.webp", "145_128-R.webp", "146_133-R.webp", "147_004-R.webp", "148_042-R.webp",
  "149_005-T.webp"
].map(filename => ({
  src: `/images/ai/ai-gallary/${filename}`,
  alt: `AI Edit ${filename.split('_')[0]}` // Simple alt text generation
}));

// Prepare slides for Lightbox
const lightboxSlides = galleryImages.map(img => ({
    src: img.src,
    alt: img.alt,
    width: 1000, // Provide approximate dimensions if known
    height: 1000,
}));


// Before/After Images
const FIRST_IMAGE = {
  imageUrl: '/images/ai/BeforeAfter/before01.webp'
};
const SECOND_IMAGE = {
  imageUrl: '/images/ai/BeforeAfter/after01.webp'
};
const THIRD_IMAGE = {
  imageUrl: '/images/ai/BeforeAfter/before02.webp'
};
const FOURTH_IMAGE = {
  imageUrl: '/images/ai/BeforeAfter/after02.webp'
};


const AiEditsPage = () => {
  // Text Constants
  const heroIntroText = (
    <>
      Hi! I’m Zvi Twersky, a <span className="font-semibold">Photoshop and Stable Diffusion AI expert.</span> In my 25 years of experience in Photoshop, I’ve developed a keen eye for what people want in an edit.
    </>
  );
  const facebookGroupTitle = "Join my “Magic Circle!” Facebook Group";
  const facebookGroupDescription = "Visit the Facebook Group to request an edit. It's the best place to see examples and interact with the community.";
  const galleryInstructions = "Swipe or use arrows to navigate. Click image to enlarge & advance.";


  // Refs
  const swiperRef = useRef(null);
  const containerRef = useRef(null);

  // State
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [activeIndex, setActiveIndex] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Handle initialization and dark mode detection
  useEffect(() => {
    // Set initialized after a delay to ensure Swiper is fully loaded
    const timer = setTimeout(() => {
      setIsInitialized(true);
    }, 500);

    // Check for dark mode
    if (typeof document !== 'undefined') {
      setIsDarkMode(document.documentElement.classList.contains('dark'));

      // Listen for dark mode changes
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.attributeName === 'class') {
            setIsDarkMode(document.documentElement.classList.contains('dark'));
          }
        });
      });

      observer.observe(document.documentElement, { attributes: true });

      return () => {
        observer.disconnect();
        clearTimeout(timer);
      };
    }

    return () => clearTimeout(timer);
  }, []);

  // Handle dot click



  // Custom slide renderer for Lightbox to handle image click
  const renderSlide = ({ slide, rect }) => {
    const { width, height } = slide; // Use dimensions from lightboxSlides if provided
    const fit = width && height ? "contain" : "cover"; // Adjust fit based on dimensions
    const slideWidth = Math.round(Math.min(rect.width, (rect.height / height) * width));
    const slideHeight = Math.round(Math.min(rect.height, (rect.width / width) * height));

    return (
      <Image
        fill={!width}
        src={slide.src}
        alt={slide.alt}
        loading="eager"
        draggable={false}
        width={width ? slideWidth : undefined}
        height={height ? slideHeight : undefined}
        style={{
            objectFit: fit,
            cursor: 'pointer',
            width: width ? slideWidth : '100%',
            height: height ? slideHeight : '100%',
            margin: '0 auto'
        }}
        sizes={`${Math.ceil((slideWidth / window.innerWidth) * 100)}vw`}
        onClick={() => {
          // Go to the next slide, wrapping around
          const nextIndex = (lightboxIndex + 1) % lightboxSlides.length;
          setLightboxIndex(nextIndex);
          // DO NOT stop propagation here - allow backdrop click to close
        }}
      />
    );
  };
  // Removed first duplicate renderSlide function
  // Removed handleDotClick function as pagination dots are removed
  // Removed second duplicate renderSlide function


  return (
    <>
      {/* Inject Swiper styles */}
      <style>{swiperStyles}</style>

      <section className="relative pb-16 pt-36 md:pb-20 lg:pb-28 lg:pt-44" style={{ position: 'relative', zIndex: 0 }}>
        <div className="container">
          {/* Hero Section */}
          <div className="mb-16 text-center">
            <h1 className="mb-4 text-4xl font-bold text-black dark:text-white sm:text-5xl md:text-[48px] md:leading-[1.2]"> {/* Increased size */}
              Save your photos with magic.
            </h1>
            <h2 className="mb-6 text-xl font-medium text-body-color sm:text-2xl"> {/* Increased size */}
              Did a photobomber ruin your cherished photo? Wish to erase an ex or visualize yourself in a bikini?
            </h2>
            <p className="mb-8 text-lg lg:text-xl text-body-color dark:text-body-color-dark"> {/* Increased size */}
              {heroIntroText}
            </p>
            <div className="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
              <Link
                href="/ai/submit-edit" // Link to the submit page
                className="neon-border-button relative inline-block rounded-lg bg-gray-100 dark:bg-gray-900 px-9 py-4 text-base font-bold text-black dark:text-white transition-all duration-300 font-orbitron hover:shadow-[0_0_15px_rgba(0,255,204,0.7)]"
              >
                Request An Edit
              </Link>
               <Link
                 href="https://www.facebook.com/groups/photoshoprequestsfreewithtippingjarmagiccircle"
                 target="_blank"
                 rel="noopener noreferrer"
                 className="rounded-md bg-primary px-8 py-3 text-lg font-semibold text-white duration-300 ease-in-out hover:bg-primary/80"
               >
                 Join Facebook Group
               </Link>
            </div>
          </div>

          {/* Combined Before/After and Satisfaction Section - Adding Spacing */}
          <div className="mb-16 grid grid-cols-1 md:grid-cols-2 gap-8"> {/* Main 2-column grid, ADDED gap-8 */}

            {/* Left Column: Wide Image Slider (Originally Right) */}
            <InteractiveBox className="p-4"> {/* Wrap with InteractiveBox, keep padding */}
              <div className="relative"> {/* Inner div for relative positioning of labels */}
                <p className="mb-2 text-center text-base text-body-color">Drag slider left/right</p> {/* Increased size */}
                <ReactBeforeSliderComponent
                  firstImage={THIRD_IMAGE}  /* Swapped: Wide image now on left */
                  secondImage={FOURTH_IMAGE}
                  className="w-full" /* Reverted h-full */
                />
                {/* Before Label */}
                <div className="absolute top-1/2 left-4 -translate-y-1/2 rounded bg-black/50 px-2 py-1 text-base font-semibold text-white z-10"> {/* Increased size, added z-10 */}
                  Before
                </div>
                {/* After Label */}
                <div className="absolute top-1/2 right-4 -translate-y-1/2 rounded bg-black/50 px-2 py-1 text-base font-semibold text-white z-10"> {/* Increased size, added z-10 */}
                  After
                </div>
              </div>
            </InteractiveBox>

            {/* Right Column: Contains Tall Image Slider and Satisfaction Box */}
            <div className="flex flex-col gap-8"> {/* Vertical stack for right column items, ADDED gap-8 */}

              {/* Top Right: Tall Image Slider (Originally Left) */}
              <InteractiveBox className="p-4"> {/* Wrap with InteractiveBox, keep padding */}
                 <div className="relative"> {/* Inner div for relative positioning of labels */}
                  <p className="mb-2 text-center text-base text-body-color">Drag slider left/right</p> {/* Increased size */}
                  <ReactBeforeSliderComponent
                    firstImage={FIRST_IMAGE} /* Swapped: Tall image now on top right */
                    secondImage={SECOND_IMAGE}
                    className="w-full" /* Reverted h-full */
                  />
                  {/* Before Label */}
                  <div className="absolute top-1/2 left-4 -translate-y-1/2 rounded bg-black/50 px-2 py-1 text-base font-semibold text-white z-10"> {/* Increased size, added z-10 */}
                    Before
                  </div>
                  {/* After Label */}
                  <div className="absolute top-1/2 right-4 -translate-y-1/2 rounded bg-black/50 px-2 py-1 text-base font-semibold text-white z-10"> {/* Increased size, added z-10 */}
                    After
                  </div>
                </div>
              </InteractiveBox>

              {/* Bottom Right: Satisfaction Box */}
              <InteractiveBox enableSpotlight={true} className="p-4 text-center flex flex-col justify-center flex-grow"> {/* Wrap, enable spotlight, keep layout classes */}
                <div> {/* Inner div for content alignment */}
                    <div className="text-6xl font-bold text-primary md:text-7xl lg:text-8xl">
                      <CountUp end={99} duration={2.5} suffix="%" enableScrollSpy scrollSpyDelay={300} />
                    </div>
                    <h3 className="mt-2 text-2xl font-semibold text-black dark:text-white md:text-3xl"> {/* Increased size */}
                      satisfaction with every edit.
                    </h3>
                  </div>
              </InteractiveBox>

            </div>
          </div>

          {/* Facebook Group Section */}
          <InteractiveBox enableSpotlight={true} className="mb-20 p-8 text-center bg-gray-light dark:bg-gray-dark"> {/* Wrap, enable spotlight, keep layout/bg classes */}
            <h3 className="mb-4 text-3xl font-bold text-black dark:text-white"> {/* Increased size */}
              {facebookGroupTitle}
            </h3>
            <p className="mb-6 text-lg lg:text-xl text-body-color dark:text-body-color-dark"> {/* Increased size */}
              {facebookGroupDescription}
            </p>
            <Link
              href="https://www.facebook.com/groups/photoshoprequestsfreewithtippingjarmagiccircle"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block rounded-md bg-blue-600 px-8 py-3 text-base font-semibold text-white duration-300 ease-in-out hover:bg-blue-700"
            >
              Visit Facebook Group
            </Link>
          </InteractiveBox>

          {/* Gallery Swiper Section */}
          <InteractiveBox className="w-full p-6 dark:bg-gray-dark"> {/* Wrap, keep padding/width/bg */}
            {/* Fixed-height container for Swiper */}
            {/* Gallery Header - Moved Inside */}
            <div className="mb-6 text-center"> {/* Adjusted margin-bottom */}
               <h2 className="mb-3 text-3xl font-bold text-black dark:text-white sm:text-4xl"> {/* Increased size */}
                 AI Edit Gallery
               </h2>
               <p className="text-lg lg:text-xl font-medium text-body-color"> {/* Increased size */}
                 {galleryInstructions}
              </p>
               <span className="mx-auto mt-4 block h-1 w-16 bg-primary"></span>
            </div>


            <div ref={containerRef} className="gallery-slider-wrapper">
              {/* Swiper with NO built-in pagination */}
              <Swiper
                ref={swiperRef}
                modules={[Navigation]} // Only Navigation needed here
                loop={true}
                spaceBetween={20} // Adjust spacing
                slidesPerView={1} // Default view
                breakpoints={{ // Responsive slides per view
                  640: { slidesPerView: 2 },
                  768: { slidesPerView: 3 },
                  1024: { slidesPerView: 4 },
                  1280: { slidesPerView: 4 }, // Changed from 5 to 4
                }}
                navigation={true}
                simulateTouch={true}
                onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
                onInit={() => setIsInitialized(true)}
                className="w-full h-full"
              >
                {galleryImages.map((image, index) => (
                  <SwiperSlide key={index} className="flex items-center justify-center">
                    <Image
                      src={image.src}
                      alt={image.alt}
                      width={250} // Adjust width as needed
                      height={250} // Adjust height to match width for square aspect ratio
                      className="h-full w-auto cursor-pointer rounded object-contain mx-auto" // Adjust to fill height, auto width, contain, and center
                      onClick={() => { setLightboxIndex(index); setLightboxOpen(true); }}
                      priority={index < 5} // Prioritize loading initial images
                    />
                  </SwiperSlide>
                ))}
              </Swiper>

              {/* Custom fixed-position pagination dots removed */}
            </div>
          </InteractiveBox>

        </div>
      </section>

      {/* Ensure space for footer top border */}
      <div className="h-8"></div>

      {/* Lightbox component */}
      <Lightbox
        open={lightboxOpen}
        close={() => setLightboxOpen(false)} // Default close action handles backdrop clicks
        index={lightboxIndex}
        slides={lightboxSlides}
        on={{
          view: ({ index: currentIndex }) => setLightboxIndex(currentIndex),
        }}
        render={{ slide: renderSlide }} // Use custom slide renderer for image clicks
        // Removed render prop to restore default backdrop click behavior
        styles={{ container: { backgroundColor: "rgba(0, 0, 0, .8)" } }} // Darker background
      />
    </>
  );
};

export default AiEditsPage;