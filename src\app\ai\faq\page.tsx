import Accordion from "@/components/Common/Accordion";
import Breadcrumb from "@/components/Common/Breadcrumb";
import { Metadata } from "next";
import Link from 'next/link'; // Import Link

export const metadata: Metadata = {
  title: "FAQ | AI Photoshop Requests",
  description: "Frequently Asked Questions about AI-powered Photoshop edits.",
  // other metadata
};

// Constants for FAQ content
const faqTitle1 = "What's considered an \"AI-powered\" edit?";
const faqContent1_1 = "You know, it's not always easy to say, since each edit is unique. For instance, swapping people, faces, or objects between images isn't AI-powered right now, as it needs manual rotation, scaling, and color/light matching. But hey, AI is improving all the time, so who knows what the future holds!";
const faqContent1_2 = "To get a feel for AI edits, check out the many examples on the home page.";
const faqContent1_3 = "In my Facebook group, please tag me in your request and I'll take a look.";

const faqTitle2 = "How many photos can I edit if I buy your book or tip you coffee?";
const faqContent2 = "Good question! One or two edits would be the sweet spot, but it all depends on the amount of work each one involves. Just tag me in your request in my Facebook group, and I'll give it a look.";

const faqTitle3 = "Can you remove people or objects from a photo?";
const faqContent3 = "You bet! That's exactly what AI-powered editing is all about.";

const faqTitle4 = "Can you change a person's clothing?";
const faqContent4 = "Absolutely! That's the magic of AI-powered editing.";

const faqTitle5 = "Can you do a face swap or move a person from one picture to another?";
const faqContent5 = "Well, as of March 2023, face swapping isn't AI-powered yet, so it needs manual color and light matching. It might be possible soon, but for now, I can only tackle these edits when I have some free time.";

const faqTitle6 = "Can you remove watermarks?";
const faqContent6 = "It depends on the watermark itself, and who owns the picture. I'll check out each request and let you know if I can help you out with it.";

const faqTitle7 = "Can you tweak a document?";
const faqContent7 = "I'd need to take a look at the request and the reason behind it. I won't edit anything illegal. I'll go through each request and give you a heads-up if I can assist you with it.";

const faqTitle8 = "Can I have you edit nude pics?";
const faqContent8 = "Sure thing, just make sure that all people in the pictures are at least 18 years old. Also, the terms of service ensure that any NSFW photos you send will stay private and secure.";

// Updated faqData structure using constants
const faqData = [
  {
    title: faqTitle1,
    content: (
      <>
        <p className="mb-4 text-base lg:text-lg">{faqContent1_1}</p>
        <p className="mb-4 text-base lg:text-lg">{faqContent1_2}</p>
        <p className="text-base lg:text-lg">{faqContent1_3}</p>
      </>
    )
  },
  {
    title: faqTitle2,
    content: <p className="text-base lg:text-lg">{faqContent2}</p>
  },
  {
    title: faqTitle3,
    content: <p className="text-base lg:text-lg">{faqContent3}</p>
  },
  {
    title: faqTitle4,
    content: <p className="text-base lg:text-lg">{faqContent4}</p>
  },
  {
    title: faqTitle5,
    content: <p className="text-base lg:text-lg">{faqContent5}</p>
  },
  {
    title: faqTitle6,
    content: <p className="text-base lg:text-lg">{faqContent6}</p>
  },
  {
    title: faqTitle7,
    content: <p className="text-base lg:text-lg">{faqContent7}</p>
  },
  {
    title: faqTitle8,
    content: <p className="text-base lg:text-lg">{faqContent8}</p>
  }
];


const FAQPage = () => {
  // Text Constants
  const contactPrompt = "Have a question not answered here? ";
  const contactAssist = ", and I’ll be happy to assist you.";

  return (
    <>
      <Breadcrumb
        pageName="Frequently Asked Questions"
        description="Find answers to common questions about our AI Photoshop editing services."
      />

      <section className="py-16 md:py-20 lg:py-24">
        <div className="container">
          <Accordion items={faqData} />
          <div className="mt-8 text-center text-gray-600 dark:text-gray-300">
             {contactPrompt}
            <Link href="/contact" className="text-primary hover:underline">
              Contact me
            </Link>
            {contactAssist}
          </div>
           <div className="mt-12 text-center">
             <Link href="/ai/submit-edit" className="rounded-md bg-primary px-8 py-3 text-base font-semibold text-white duration-300 ease-in-out hover:bg-primary/80">
             Request An Edit
             </Link>
           </div>
        </div>
      </section>
    </>
  );
};

export default FAQPage;