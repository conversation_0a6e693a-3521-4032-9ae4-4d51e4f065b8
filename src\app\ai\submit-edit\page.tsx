"use client";

import Image from "next/image";
import Link from "next/link";
import React, { useState, useRef, useEffect } from 'react';

// Import Swiper React components
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

// Import Lightbox
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";

// Add custom Swiper pagination and navigation styles
const swiperStyles = `
  /* Swiper Container - Fixed height and positioning */
  .book-slider-wrapper {
    position: relative !important;
    height: 420px !important; /* Fixed height to prevent layout shifts */
    margin-bottom: 40px !important; /* Space for pagination */
    overflow: hidden !important;
  }

  /* Hide Swiper's built-in pagination completely */
  .swiper-pagination,
  .swiper-pagination-bullets,
  .swiper-pagination-horizontal {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
  }

  /* Navigation button positioning */
  .book-slider-wrapper .swiper-button-prev,
  .book-slider-wrapper .swiper-button-next {
    top: 40% !important;
    z-index: 20 !important;
  }

  /* Fixed-position custom pagination container */
  .fixed-pagination {
    position: absolute !important;
    bottom: 1px !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 10px 0 !important;
    z-index: 10 !important;
    background: transparent !important;
  }

  /* Custom pagination dot styling */
  .pagination-dot {
    width: 10px !important;
    height: 10px !important;
    border-radius: 50% !important;
    background-color: #cbd5e1 !important;
    cursor: pointer !important;
    opacity: 0.7 !important;
    transition: opacity 0.3s, background-color 0.3s !important;
  }

  .pagination-dot.active {
    opacity: 1 !important;
    background-color: #0ea5e9 !important;
  }

  /* Dark mode pagination dot colors */
  .dark .pagination-dot {
    background-color: #3b82f6 !important;
  }

  .dark .pagination-dot.active {
    background-color: #22c55e !important;
  }
`;

const books = [
  {
    title: "Children's book",
    imgSrc: "/images/books/Fly-Into-Your-Heart-FRONT.webp",
    alt: "Fly Into Your Heart",
    amazonLink: "https://www.amazon.com/Fly-Into-Your-Heart-Smart/dp/B0CKMHSMG6",
  },
  {
    title: "MEDICAL CROSSWORDS",
    imgSrc: "/images/books/007-MEDICAL-CROSSWORDS-PAPERBACK-FRONT.webp",
    alt: "Medical Crosswords",
    amazonLink: "https://www.amazon.com/dp/B09SP1FRVK",
  },
  {
    title: "CROSSWORDS",
    imgSrc: "/images/books/008-MEDICAL-CROSSWORDS-KIDS-PAPERBACK-FRONT.webp",
    alt: "Medical Crosswords Kids",
    amazonLink: "https://www.amazon.com/dp/B0B86H2BPP",
  },
  {
    title: "WORD SEARCH",
    imgSrc: "/images/books/010-MEDICAL-WORD-SEARCH-PAPERBACK-FRONT.webp",
    alt: "Medical Word Search",
    amazonLink: "https://www.amazon.com/dp/B0BBXT2T2G",
  },
  {
    title: "WORD SEARCH",
    imgSrc: "/images/books/011-ENLIGHTENING-WORD-SEARCH-PAPERBACK-FRONT.webp",
    alt: "Enlightening Word Search",
    amazonLink: "https://www.amazon.com/dp/B0BGKL7P19",
  },
  {
    title: "WORD SEARCH",
    imgSrc: "/images/books/012-CHRISTMAS-WORD-SEARCH-PAPERBACK-FRONT-RGB.webp",
    alt: "Christmas Word Search",
    amazonLink: "https://www.amazon.com/dp/B0BJ7Y254P",
  },
  {
    title: "WORD SEARCH PUZZLES",
    imgSrc: "/images/books/009-Crossing-the-Decades-to-1950-PAPERBACK-FRONT-RGB.webp",
    alt: "Crossing the Decades Word Search",
    amazonLink: "https://www.amazon.com/dp/B0BZF7M4TM",
  },
];

// Prepare slides for Lightbox
const lightboxSlides = books.map(book => ({ src: book.imgSrc, alt: book.alt }));

const SubmitEditPage = () => {
  // Text Constants
  const pageTitle = "Ready for your AI-Powered edits and perks?";
  const pageSubtitle = "Just follow the 2 steps below…";
  const step1Title = 'Join the "Magic Circle!"';
  const step1Description = "Join my Facebook Group. Put in a request, and I'll check it out.";
  const itsText = "It's"; // Constant for the word with an apostrophe
  const step2Description = (
    <>
      Loved my edit? {itsText} <span className="font-bold text-green-600">100% optional</span> for you to support me by buying one of my books on Amazon and leaving a review, or treat me to some coffee. ☕ (links below)
    </>
  );
  const bookSliderInstructions = "Swipe or use arrows to navigate. Click image to zoom.";

  // Refs
  const swiperRef = useRef<any>(null);
  const containerRef = useRef(null);

  // State
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [activeIndex, setActiveIndex] = useState(0);
  
  // No initialization effect needed
  
  // Handle dot click
  const handleDotClick = (index: number) => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideToLoop(index);
    }
  };

  return (
    <>
      {/* Inject Swiper styles */}
      <style>{swiperStyles}</style>

      <section className="relative z-10 overflow-hidden pb-16 pt-36 md:pb-20 lg:pb-28 lg:pt-44">
        <div className="container">
          {/* Top Title */}
          <div className="mb-12 text-center">
             <h2 className="mb-3 text-2xl font-bold text-black dark:text-white sm:text-3xl">
               {pageTitle}
             </h2>
             <p className="text-base lg:text-lg font-medium text-body-color">
               {pageSubtitle}
             </p>
             <span className="mx-auto mt-4 block h-1 w-16 bg-primary"></span>
          </div>

          {/* Steps Section - Two Columns */}
          <div className="mb-16 flex flex-wrap justify-center gap-8 md:flex-nowrap">
            {/* Step 1 Column */}
            <div className="w-full max-w-md rounded border border-stroke p-6 shadow-three dark:border-dark-3 dark:bg-gray-dark dark:shadow-none md:w-1/2">
              <h3 className="mb-4 text-center text-xl font-bold text-black dark:text-white">
                Step #1
              </h3>
               <h4 className="mb-4 text-center text-lg font-semibold text-black dark:text-white">
                 {step1Title}
               </h4>
              <p className="mb-4 text-center text-base lg:text-lg font-medium text-body-color">
                {step1Description}
              </p>
              <div className="mb-6 rounded border border-stroke bg-gray-light p-4 dark:border-dark-3 dark:bg-dark">
                <p className="text-center text-base lg:text-lg text-body-color dark:text-body-color-dark">
                  Visit the{" "}
                  <a
                    href="https://www.facebook.com/groups/photoshoprequestsfreewithtippingjarmagiccircle"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:underline"
                  >
                    Facebook Group
                  </a>{" "}
                  to request an edit.
                </p>
              </div>
            </div>

            {/* Step 2 Column */}
            <div className="w-full max-w-md rounded border border-stroke p-6 shadow-three dark:border-dark-3 dark:bg-gray-dark dark:shadow-none md:w-1/2">
               <h3 className="mb-4 text-center text-xl font-bold text-black dark:text-white">
                 Step #2
               </h3>
              <p className="mb-8 text-center text-base lg:text-lg font-medium text-body-color">
                {step2Description}
              </p>
            </div>
          </div>

          {/* Bottom Section - Two Columns */}
          <div className="flex flex-wrap justify-center gap-8 md:flex-nowrap">
            {/* Book Slider Column */}
            <div className="w-full overflow-hidden rounded border border-stroke p-6 shadow-three dark:border-dark-3 dark:bg-gray-dark dark:shadow-none md:w-5/12">
              <p className="mb-4 text-center text-sm text-body-color">{bookSliderInstructions}</p>
              
              {/* Fixed-height container with ref for measurements */}
              <div ref={containerRef} className="book-slider-wrapper">
                {/* Swiper with NO pagination */}
                <Swiper
                  ref={swiperRef}
                  modules={[Navigation]}
                  loop={true}
                  spaceBetween={10}
                  slidesPerView={2}
                  navigation={true}
                  simulateTouch={true}
                  onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
                  className="w-full h-full"
                >
                  {books.map((book, index) => (
                    <SwiperSlide key={index} className="text-center">
                      <Image
                        src={book.imgSrc}
                        alt={book.alt}
                        width={200}
                        height={300}
                        className="mx-auto mb-4 h-auto w-full max-w-[220px] cursor-pointer rounded"
                        onClick={() => { setLightboxIndex(index); setLightboxOpen(true); }}
                      />
                      <h4 className="mb-2 text-sm lg:text-base font-semibold text-black dark:text-white">
                        {book.title}
                      </h4>
                      <a
                        href={book.amazonLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-block rounded bg-orange-500 px-4 py-2 text-xs font-medium text-white hover:bg-orange-600 mb-4"
                      >
                        BUY ON AMAZON
                      </a>
                    </SwiperSlide>
                  ))}
                </Swiper>
                
                {/* Custom fixed-position pagination dots */}
                <div className="fixed-pagination">
                  {books.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => handleDotClick(index)}
                      className={`pagination-dot ${index === activeIndex ? 'active' : ''}`}
                      aria-label={`Go to slide ${index + 1}`}
                    />
                  ))}
                </div>
                
              </div>
            </div>

            {/* Ko-fi Embed Column */}
            <div className="max-w-md border border-stroke p-6 shadow-three dark:border-dark-3 dark:bg-gray-dark dark:shadow-none md:w-5/12">
              <iframe
                id='kofiframe'
                src='https://ko-fi.com/ztwersky/?hidefeed=true&widget=true&embed=true&preview=true'
                className='h-[650px] w-full border-none'
                title='ztwersky'
              ></iframe>
            </div>
          </div>
        </div>
      </section>

      {/* Lightbox component */}
      <Lightbox
        open={lightboxOpen}
        close={() => setLightboxOpen(false)}
        index={lightboxIndex}
        slides={lightboxSlides}
        on={{
          view: ({ index: currentIndex }) => setLightboxIndex(currentIndex),
          click: () => setLightboxOpen(false),
        }}
      />
    </>
  );
};

export default SubmitEditPage;