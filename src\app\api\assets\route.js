import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// This route handler is a fallback to ensure SVG files have noindex headers
// It's used in addition to the middleware for redundancy

export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const filePath = searchParams.get('path');
  
  // Security check to prevent directory traversal
  if (!filePath || filePath.includes('..')) {
    return new NextResponse('Invalid file path', { status: 400 });
  }
  
  // Construct the full path to the file
  const fullPath = path.join(process.cwd(), 'public', filePath);
  
  try {
    // Check if the file exists
    if (!fs.existsSync(fullPath)) {
      return new NextResponse('File not found', { status: 404 });
    }
    
    // Read the file
    const fileContent = fs.readFileSync(fullPath);
    
    // Determine content type based on file extension
    const ext = path.extname(filePath).toLowerCase();
    let contentType = 'application/octet-stream';
    
    if (ext === '.svg') {
      contentType = 'image/svg+xml';
    } else if (ext === '.png') {
      contentType = 'image/png';
    } else if (ext === '.jpg' || ext === '.jpeg') {
      contentType = 'image/jpeg';
    } else if (ext === '.gif') {
      contentType = 'image/gif';
    } else if (ext === '.woff') {
      contentType = 'font/woff';
    } else if (ext === '.woff2') {
      contentType = 'font/woff2';
    } else if (ext === '.ttf') {
      contentType = 'font/ttf';
    } else if (ext === '.eot') {
      contentType = 'application/vnd.ms-fontobject';
    }
    
    // Return the file with noindex header
    return new NextResponse(fileContent, {
      headers: {
        'Content-Type': contentType,
        'X-Robots-Tag': 'noindex',
        'Cache-Control': 'public, max-age=86400, s-maxage=86400'
      }
    });
  } catch (error) {
    console.error('Error serving file:', error);
    return new NextResponse('Error serving file', { status: 500 });
  }
}
