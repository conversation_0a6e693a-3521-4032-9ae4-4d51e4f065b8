import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: Request) {
  const formData = await request.formData();

  // Verify Turnstile token
  const token = formData.get('cf-turnstile-response');
  const ip = request.headers.get('x-forwarded-for')?.split(',')[0].trim() ||
            request.headers.get('x-real-ip')?.trim() ||
            'Unknown';

  // Detect if we're in development/localhost
  const isLocalhost = ip === '::1' || ip === '127.0.0.1' || ip === 'localhost';
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Always use the production secret key
  const secretKey = process.env.TURNSTILE_SECRET_KEY;

  console.log('Using secret key type:', isDevelopment || isLocalhost ? 'DEV' : 'PROD');

  // Single verification logic using FormData
  const formDataVerify = new FormData();
  formDataVerify.append('secret', secretKey || ''); // Ensure secretKey is not undefined
  formDataVerify.append('response', token || '');   // Ensure token is not undefined
  formDataVerify.append('remoteip', ip);

  const turnstileResponse = await fetch(
    'https://challenges.cloudflare.com/turnstile/v0/siteverify',
    {
      method: 'POST',
      body: formDataVerify, // Send as FormData
    }
  );

  const turnstileData = await turnstileResponse.json();
  console.log('Turnstile verification response:', turnstileData);

  if (!turnstileData.success) {
    return NextResponse.json({ error: 'Invalid captcha' }, { status: 400 });
  }

  // Check honeypot field - if it's filled, it's a bot
  const favorite_color = formData.get('favorite_color');
  if (favorite_color) {
    console.log(`API: Bot detected! Honeypot field was filled. ${isLocalhost ? '(Testing on localhost)' : ''}`);

    // Add more detailed message for localhost testing
    const errorMessage = isLocalhost
      ? "TESTING: Bot detection successful! The honeypot field was filled. In a real scenario, this form submission would be blocked."
      : "We've detected that you're a bot. This form cannot be submitted.";

    return NextResponse.json(
      { error: errorMessage },
      { status: 403 }
    );
  }

  // Get geolocation data
  let geoLocation = 'Local Development (No Geolocation Available)';
  // isLocalhost is already defined above, reuse it

  if (!isLocalhost && ip !== 'Unknown' && process.env.IP2LOCATION_API_KEY) {
    try {
      const geoResponse = await fetch(
        `https://api.ip2location.io/?key=${process.env.IP2LOCATION_API_KEY}&ip=${ip}`
      );
      if (geoResponse.ok) {
        const ipData = await geoResponse.json();
        geoLocation = `${ipData.city_name || ''}, ${ipData.region_name || ''}, ${ipData.country_name || ''}`;
      }
    } catch (error) {
      console.error('Geolocation error:', error);
    }
  }

  // Simple SMTP check
  if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASSWORD || !process.env.SMTP_EMAIL) {
    console.error('SMTP configuration missing. Check your environment variables.');
    return NextResponse.json({ error: 'Email configuration error' }, { status: 500 });
  }

  // Send email with simple configuration
  console.log('Creating transporter with config:', {
    host: process.env.SMTP_HOST,
    user: '***' // Hide for security
  });

  // Create transporter with minimal configuration
  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: 587, // Standard SMTP port
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD,
    }
  });

  try {
    // Send mail using the simple configuration
    const info = await transporter.sendMail({
      from: process.env.SMTP_EMAIL,
      to: process.env.SMTP_EMAIL,
      subject: `New contact form submission from ${formData.get('name') || 'Unknown'}`,
      text: `
        Name: ${formData.get('name')}
        Email: ${formData.get('email')}
        Message: ${formData.get('message')}

        Additional Info:
        IP: ${ip}
        Location: ${geoLocation}
        Time: ${new Date().toLocaleString()}
        Honeypot Check: Passed (No bot detected)
      `,
      html: `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>New Contact Submission</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6;">
            <table width="100%" border="0" cellpadding="20" style="max-width: 600px; margin: 0 auto; border: 1px solid #e0e0e0;">
                <tr>
                    <td style="background-color: #f8f9fa; border-bottom: 2px solid #007bff;">
                        <h1 style="color: #2d3436; margin: 0;">New Contact Form Submission</h1>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" border="0" cellpadding="10">
                            <tr>
                                <td width="30%" style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6;"><strong>Name:</strong></td>
                                <td width="70%" style="padding: 8px; border: 1px solid #dee2e6;">${formData.get('name')}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6;"><strong>Email:</strong></td>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">${formData.get('email')}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6;"><strong>Message:</strong></td>
                                <td style="padding: 8px; border: 1px solid #dee2e6; white-space: pre-wrap;">${formData.get('message')}</td>
                            </tr>
                        </table>

                        <h3 style="color: #2d3436; margin-top: 25px; margin-bottom: 15px;">Submission Details</h3>
                        <table width="100%" border="0" cellpadding="10" style="background-color: #f8f9fa; border: 1px solid #dee2e6;">
                            <tr>
                                <td width="30%" style="padding: 8px;"><strong>IP Address:</strong></td>
                                <td width="70%" style="padding: 8px;">${ip}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px;"><strong>Location:</strong></td>
                                <td style="padding: 8px;">${geoLocation}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px;"><strong>Timestamp:</strong></td>
                                <td style="padding: 8px;">${new Date().toLocaleString()}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px;"><strong>Honeypot Check:</strong></td>
                                <td style="padding: 8px;">Passed (No bot detected)</td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </body>
        </html>
      `,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Email error:', error);
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    );
  }
}