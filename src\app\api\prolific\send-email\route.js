import nodemailer from 'nodemailer';
import { NextResponse } from 'next/server';

// In-memory storage since we can't write to filesystem in serverless environment
let cachedUserData = null;

export async function POST(request) {
  try {
    // Parse the request body
    const data = await request.json();
    const action = data.action; // Check for specific actions if needed

    console.log('Received POST data:', data);

    // Handle user data storage - emergency backup only (if action exists)
    if (action === 'store_user_data') {
      console.log('BACKUP: Storing user data in memory (serverless environment):', data.userData);
      if (!data.userData) {
        console.warn('Missing userData in request for store_user_data action');
        return NextResponse.json({ success: false, message: 'Missing userData in request' }, { status: 400 });
      }
      try {
        cachedUserData = data.userData;
        console.log('Successfully stored user data in backup memory');
        return NextResponse.json({ success: true, message: 'User data stored successfully in backup memory' });
      } catch (error) {
        console.error('Error storing user data in backup:', error.message, error.stack);
        return NextResponse.json({ success: false, message: 'Failed to store user data in backup: ' + error.message }, { status: 500 });
      }
    }

    // --- Email Sending Logic ---
    const { from, to, subject, body, studyList } = data;

    // Validate essential email fields
    if (!from || !to || !subject) {
      return NextResponse.json({ success: false, message: 'Missing required fields: from, to, or subject' }, { status: 400 });
    }

    // Determine if we should use studyList or body
    const useStudyList = Array.isArray(studyList) && studyList.length > 0;
    if (!useStudyList && typeof body !== 'string') {
        // If not using studyList, we MUST have a body string
        return NextResponse.json({ success: false, message: 'Missing required field: body (or invalid studyList)' }, { status: 400 });
    }

    // Check SMTP configuration
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER ||
        !process.env.SMTP_PASSWORD || !process.env.SMTP_EMAIL) {
      console.error('SMTP configuration missing');
      return NextResponse.json({ success: false, message: 'Email configuration error' }, { status: 500 });
    }

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: 587,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD,
      }
    });

    // Generate HTML and Text content based on available data
    const htmlContent = generateFormattedEmail(subject, useStudyList ? studyList : body, useStudyList);
    const textContent = useStudyList
        ? studyList.map(study =>
            `Study: ${study.studyName || 'N/A'}\n` +
            `Host: ${study.studyHost || 'N/A'}\n` +
            `Reward: ${study.studyReward || 'N/A'} (${study.studyRewardPerHour || 'N/A'})\n` +
            `URL: ${study.studyUrl || 'N/A'}\n` +
            `Found at: ${study.studyFoundAt || 'N/A'}\n` + // Use the already formatted date from client
            `----------------------------------------`
          ).join('\n\n')
        : (body || ''); // Use body string if not using studyList

    // Send email
    await transporter.sendMail({
      from: from,
      to: to,
      subject: subject,
      text: textContent,
      html: htmlContent
    });

    return NextResponse.json({ success: true, message: 'Email sent successfully' });

  } catch (error) {
    console.error('API error:', error);
    // Avoid exposing detailed error messages in production if possible
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({
      success: false,
      message: 'Failed to process request: ' + errorMessage
    }, { status: 500 });
  }
}

// Function to generate a well-formatted HTML email
// Accepts either an array of study objects or a plain text body
function generateFormattedEmail(subject, contentData, isStudyData) {
  const timestamp = new Date().toLocaleString();
  const primaryColor = '#007bff';
  const backgroundColor = '#f8f9fa';
  const borderColor = '#dee2e6';
  const textColor = '#2d3436';

  let messageBoxContent = '';

  if (isStudyData && Array.isArray(contentData)) {
    // --- Process array of study objects (Study Alerts) ---
    messageBoxContent = contentData.map(study => {
      const studyName = study.studyName || 'N/A';
      const studyHost = study.studyHost || 'N/A';
      const studyReward = study.studyReward || 'N/A';
      const studyRewardPerHour = study.studyRewardPerHour || 'N/A';
      const studyUrl = study.studyUrl || '';
      const studyFoundAt = study.studyFoundAt || 'N/A'; // Use the formatted date string from client

      let urlHtml = studyUrl; // Default to plain text
      if (studyUrl.toLowerCase().startsWith('http://') || studyUrl.toLowerCase().startsWith('https://')) {
        urlHtml = `<a href="${studyUrl}" target="_blank" style="color: #0056b3; text-decoration: underline;">${studyUrl}</a>`;
      }

      // Use paragraph tags for better spacing and structure
      return `
        <div class="study-block">
          <p><span class="info-label">Study:</span> ${studyName}</p>
          <p><span class="info-label">Host:</span> ${studyHost}</p>
          <p><span class="info-label">Reward:</span> ${studyReward} (${studyRewardPerHour})</p>
          <p><span class="info-label">URL:</span> ${urlHtml}</p>
          <p><span class="info-label">Found at:</span> ${studyFoundAt}</p>
        </div>
      `;
    }).join('');
  } else if (typeof contentData === 'string') {
    // --- Process plain text body (Fallback / User Data Changes / Other Alerts) ---
    // Attempt to group related lines (Field, Old, New) for better formatting
    const lines = contentData.split('\n');
    let currentBlock = [];
    let formattedBlocks = [];

    lines.forEach(line => {
        const trimmedLine = line.trim();
        if (trimmedLine === '') {
            // If we encounter an empty line and have a current block, process it
            if (currentBlock.length > 0) {
                formattedBlocks.push(processTextBlock(currentBlock));
                currentBlock = []; // Reset for next block
            }
        } else {
            currentBlock.push(trimmedLine);
        }
    });
    // Process any remaining lines in the last block
    if (currentBlock.length > 0) {
        formattedBlocks.push(processTextBlock(currentBlock));
    }

    messageBoxContent = formattedBlocks.join(''); // Join the formatted HTML blocks

  } else {
     messageBoxContent = '<p>Error: Invalid content data format.</p>';
  }

  // Ensure CSS is complete
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: ${textColor}; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 20px auto; border: 1px solid ${borderColor}; border-radius: 4px; overflow: hidden; background-color: #ffffff; }
            .header { background-color: ${primaryColor}; color: white; padding: 20px; text-align: center; }
            .header h1 { margin: 0; font-size: 24px; }
            .content { padding: 20px; }
            .footer { background-color: ${backgroundColor}; padding: 15px; text-align: center; font-size: 12px; color: #6c757d; border-top: 1px solid ${borderColor}; }
            .message-box { background-color: ${backgroundColor}; border: 1px solid ${borderColor}; border-radius: 4px; padding: 15px; margin-top: 15px; }
            .info-label { font-weight: bold; color: ${primaryColor}; }
            .study-block { margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #eee; }
            .study-block:last-child { border-bottom: none; margin-bottom: 0; padding-bottom: 0; }
            .study-block p { margin: 5px 0; }
            .change-block { margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px dashed #ccc; } /* Style for user data changes */
            .change-block:last-child { border-bottom: none; margin-bottom: 0; padding-bottom: 0; }
            .change-block p { margin: 2px 0; font-size: 0.95em; } /* Compact spacing for change details */
            .general-text p { margin: 10px 0; } /* Default spacing for other text */
            a { color: #0056b3; text-decoration: underline; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${subject}</h1>
            </div>
            <div class="content">
                <div class="message-box">
                    ${messageBoxContent}
                </div>
            </div>
            <div class="footer">
                <p>This is an automated message from the Prolific Server System.</p>
                <p>Sent at: ${timestamp}</p>
            </div>
        </div>
    </body>
    </html>
  `;
}

// Helper function to process a block of text lines (used for fallback formatting)
function processTextBlock(lines) {
    // Simple check: if lines contain "Field:", "Old value:", "New value:", format as a change block
    const isChangeBlock = lines.some(l => l.startsWith('Field:')) &&
                          lines.some(l => l.startsWith('Old value:')) &&
                          lines.some(l => l.startsWith('New value:'));

    // Escape HTML function
    const escapeHtml = (unsafe) => {
      if (!unsafe) return '';
      return unsafe
           .replace(/&/g, "&amp;")
           .replace(/</g, "&lt;")
           .replace(/>/g, "&gt;")
           .replace(/"/g, "&quot;")
           .replace(/'/g, "&#039;");
  };

    if (isChangeBlock) {
        let field = '', oldVal = '', newVal = '';
        lines.forEach(line => {
            if (line.startsWith('Field:')) field = line.substring(6).trim();
            else if (line.startsWith('Old value:')) oldVal = line.substring(10).trim();
            else if (line.startsWith('New value:')) newVal = line.substring(10).trim();
        });
        // Format as a compact block
        return `<div class="change-block">
                  <p><span class="info-label">Field:</span> ${escapeHtml(field)}</p>
                  <p><span style="color: #dc3545;">Old:</span> ${escapeHtml(oldVal)}</p>
                  <p><span style="color: #28a745;">New:</span> ${escapeHtml(newVal)}</p>
                </div>`;
    } else {
        // Otherwise, treat as general text, wrap lines in paragraphs
        return `<div class="general-text">${lines.map(line => `<p>${escapeHtml(line) || '&nbsp;'}</p>`).join('')}</div>`;
    }
}