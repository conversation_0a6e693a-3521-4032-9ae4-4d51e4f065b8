import { NextResponse } from 'next/server';
import { supabase } from '@/utils/supabase';

// Function to store study data in Supabase
async function saveStudyDataToSupabase(studyData) {
  try {
    console.log('Storing study data to Supabase:', studyData);

    // First, check if any records exist
    const { data: existingData, error: selectError } = await supabase
      .from('prolific_study_data')
      .select('id')
      .order('id', { ascending: true });

    if (selectError) {
      console.error('Error checking for existing study data in Supabase:', selectError.message);
      return false;
    }

    let result;

    if (existingData && existingData.length > 0) {
      // We have existing records

      // If there's more than one record, we'll keep only the first one and delete the rest
      if (existingData.length > 1) {
        console.log(`Found ${existingData.length} records, keeping only the first one (id: ${existingData[0].id})`);

        // Get all IDs except the first one
        const idsToDelete = existingData.slice(1).map(item => item.id);

        // Delete extra records
        const { error: deleteError } = await supabase
          .from('prolific_study_data')
          .delete()
          .in('id', idsToDelete);

        if (deleteError) {
          console.error('Error deleting extra records:', deleteError.message);
          // Continue anyway, we'll just update the first record
        }
      }

      // Update the first record
      const { data, error } = await supabase
        .from('prolific_study_data')
        .update({
          study_url: studyData.study_url,
          study_id: studyData.study_id,
          study_name: studyData.study_name,
          researcher_name: studyData.researcher_name,
          studyReward: studyData.studyReward,
          hourly_rate: studyData.hourly_rate,
          completion_code: studyData.completion_code || '',
          study_IsUnderPaid: studyData.study_IsUnderPaid === true
        })
        .eq('id', existingData[0].id)
        .select();

      result = { success: !error, error, data };
    } else {
      // No records exist, insert a new one
      const { data, error } = await supabase
        .from('prolific_study_data')
        .insert({
          study_url: studyData.study_url,
          study_id: studyData.study_id,
          study_name: studyData.study_name,
          researcher_name: studyData.researcher_name,
          studyReward: studyData.studyReward,
          hourly_rate: studyData.hourly_rate,
          completion_code: studyData.completion_code || '',
          study_IsUnderPaid: studyData.study_IsUnderPaid === true
        })
        .select();

      result = { success: !error, error, data };
    }

    if (!result.success) {
      console.error('Error storing study data in Supabase:', result.error.message);
      return false;
    }

    console.log('Successfully stored study data in Supabase');
    return true;
  } catch (error) {
    console.error('Error storing study data in Supabase:', error.message, error.stack);
    return false;
  }
}

// Function to get latest study data from Supabase
async function getLatestStudyDataFromSupabase() {
  try {
    console.log('Fetching study data from Supabase');

    // Get all records ordered by ID (ascending)
    const { data, error } = await supabase
      .from('prolific_study_data')
      .select('*')
      .order('id', { ascending: true });

    if (error) {
      console.error('Error fetching study data from Supabase:', error.message);
      return null;
    }

    if (!data || data.length === 0) {
      console.log('No study data found in Supabase');
      return null;
    }

    // If we have multiple records, we should clean up and keep only the first one
    if (data.length > 1) {
      console.log(`Found ${data.length} records, should only have one. Using the first record and cleaning up extras.`);

      // Get all IDs except the first one
      const idsToDelete = data.slice(1).map(item => item.id);

      // Delete extra records
      const { error: deleteError } = await supabase
        .from('prolific_study_data')
        .delete()
        .in('id', idsToDelete);

      if (deleteError) {
        console.error('Error deleting extra records:', deleteError.message);
        // Continue anyway, we'll just return the first record
      } else {
        console.log(`Deleted ${idsToDelete.length} extra records, keeping only id: ${data[0].id}`);
      }
    }

    console.log('Retrieved study data from Supabase');
    return data[0];
  } catch (error) {
    console.error('Error getting latest study data from Supabase:', error.message);
    return null;
  }
}

// Function to store study data in Supabase
async function saveStudyData(studyData) {
  try {
    // Store in Supabase
    return await saveStudyDataToSupabase(studyData);
  } catch (error) {
    console.error('Error storing study data:', error.message, error.stack);
    return false;
  }
}

// Function to get latest study data from Supabase
async function getLatestStudyData() {
  try {
    // Get from Supabase
    return await getLatestStudyDataFromSupabase();
  } catch (error) {
    console.error('Error getting latest study data:', error.message);
    return null;
  }
}

// GET handler
export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action');

  if (action === 'get_study') {
    const studyData = await getLatestStudyData();

    // Determine if we actually have meaningful data
    const hasData = studyData && studyData.study_name && studyData.study_id;

    return NextResponse.json({
      success: hasData, // Only report success if we have actual data
      study_url: studyData?.study_url || null,
      study_id: studyData?.study_id || null,
      study_name: studyData?.study_name || null,
      researcher_name: studyData?.researcher_name || null,
      studyReward: studyData?.studyReward || null,
      hourly_rate: studyData?.hourly_rate || null,
      completion_code: studyData?.completion_code || null,
      study_IsUnderPaid: studyData?.study_IsUnderPaid || null,
      message: hasData ? 'Latest study data retrieved' : 'No study data available',
      source: 'supabase',
      db_id: studyData?.id || null // Include database ID if available
    });
  }

  return NextResponse.json({ success: false, message: 'Invalid action' }, { status: 400 });
}

// POST handler
export async function POST(request) {
  try {
    // Parse the JSON body
    const data = await request.json();
    const action = data.action;

    console.log('Received POST data:', data);

    // Handle study data storage - only for the specific 'store_study' action
    if (action === 'store_study') {
      // Validate required fields
      if (!data.study_url || !data.study_id || !data.study_name || !data.researcher_name ||
          !data.studyReward || !data.hourly_rate) {

        // We'll be more lenient with completion_code and study_IsUnderPaid, as they might not always be available
        console.warn('Missing some fields in study data:', data);

        return NextResponse.json({
          success: false,
          message: 'Missing required fields'
        }, { status: 400 });
      }

      // Store the study data
      const success = await saveStudyData({
        study_url: data.study_url,
        study_id: data.study_id,
        study_name: data.study_name,
        researcher_name: data.researcher_name,
        studyReward: data.studyReward,
        hourly_rate: data.hourly_rate,
        completion_code: data.completion_code || '',
        study_IsUnderPaid: data.study_IsUnderPaid === true
      });

      return NextResponse.json({
        success,
        message: success ? 'Study data stored successfully' : 'Failed to store study data',
        storage: 'supabase'
      });
    }

    return NextResponse.json({
      success: false,
      message: 'Invalid action'
    }, { status: 400 });
  } catch (error) {
    console.error('Error processing request:', error);
    return NextResponse.json({
      success: false,
      message: 'Error processing request: ' + error.message
    }, { status: 500 });
  }
}