import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

// GitHub repository details
const REPO_OWNER = 'zvit';
const REPO_NAME = 'crayolex.com';
const USER_DATA_FILE_PATH = 'src/app/prolific/data/prolific-user-data.json';
const GITHUB_API_TOKEN = process.env.GITHUB_API_TOKEN;
// SMTP configuration for sending emails
const SMTP_HOST = process.env.SMTP_HOST;
const SMTP_USER = process.env.SMTP_USER;
const SMTP_PASSWORD = process.env.SMTP_PASSWORD;
const SMTP_EMAIL = process.env.SMTP_EMAIL;
const NOTIFICATION_EMAIL = '<EMAIL>'; // Email to receive notifications

// Fields to filter out from Prolific user data - both for notifications and storage
const fieldsToFilterOut = ['_metadata', 'active_study_id', 'pending_balance', 'balance', '_links', 'estimated_total_balance', 'balance_by_currency', 'pending_balance_by_currency', 'estimated_total_pending_balance', 'pending_balance_by_currency', 'cashout_errors', 'firebase_token'];

// Function to encode content to Base64
function encodeToBase64(content) {
  return Buffer.from(content).toString('base64');
}

// Function to validate GitHub token
async function validateGitHubToken() {
  console.log("Validating GitHub token...");

  // Check if token exists
  if (!GITHUB_API_TOKEN || GITHUB_API_TOKEN.trim() === '') {
    console.error("GitHub token is missing or empty");
    return { valid: false, reason: "Token is missing or empty" };
  }

  try {
    const response = await fetch('https://api.github.com/user', {
      headers: {
        'Authorization': `token ${GITHUB_API_TOKEN}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'crayolex-app'
      }
    });

    if (response.status === 200) {
      const userData = await response.json();
      console.log(`GitHub token is valid. Authenticated as: ${userData.login}`);
      return { valid: true };
    } else if (response.status === 401) {
      console.error("GitHub token is invalid or expired");
      return { valid: false, reason: "Token is invalid or expired" };
    } else if (response.status === 403) {
      console.error("GitHub token rate limit exceeded or insufficient permissions");
      return { valid: false, reason: "Rate limit exceeded or insufficient permissions" };
    } else {
      const errorBody = await response.text();
      console.error(`GitHub token validation failed with status ${response.status}: ${errorBody}`);
      return { valid: false, reason: `Status ${response.status}: ${errorBody}` };
    }
  } catch (error) {
    console.error("Error validating GitHub token:", error.message);
    return { valid: false, reason: `Error: ${error.message}` };
  }
}

// Function to fetch a file from GitHub
async function getFileFromGitHub(filePath) {
  console.log(`Fetching file from GitHub: ${filePath}`);

  try {
    // First, check if the token is available
    if (!GITHUB_API_TOKEN || GITHUB_API_TOKEN.trim() === '') {
      console.error('GitHub API token is missing or empty');
      return null;
    }

    const url = `https://api.github.com/repos/${REPO_OWNER}/${REPO_NAME}/contents/${filePath}`;
    console.log(`Making GitHub API request to: ${url}`);

    const response = await fetch(url, {
      headers: {
        'Authorization': `token ${GITHUB_API_TOKEN}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'crayolex-app'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      const statusCode = response.status;
      console.error(`GitHub API error (${statusCode}): ${errorText}`);

      // Check for specific errors
      if (statusCode === 401) {
        console.error('GitHub token is invalid or expired. Please generate a new token.');
      } else if (statusCode === 403) {
        console.error('GitHub API rate limit exceeded or insufficient permissions.');
      } else if (statusCode === 404) {
        console.error(`File not found: ${filePath}`);
      }

      return null;
    }

    const data = await response.json();

    // Decode content from Base64
    const content = Buffer.from(data.content, 'base64').toString('utf8');
    return {
      content: content,
      sha: data.sha
    };
  } catch (error) {
    console.error('Error fetching file from GitHub:', error.message, error.stack);
    return null;
  }
}

// Function to update a file on GitHub
async function updateFileOnGitHub(filePath, content, sha) {
  console.log(`Updating file on GitHub: ${filePath}`);

  try {
    // First, check if the token is available
    if (!GITHUB_API_TOKEN || GITHUB_API_TOKEN.trim() === '') {
      console.error('GitHub API token is missing or empty');
      return false;
    }

    const url = `https://api.github.com/repos/${REPO_OWNER}/${REPO_NAME}/contents/${filePath}`;

    const payload = {
      message: `Update ${filePath} via API`,
      content: encodeToBase64(content),
      sha: sha
    };

    console.log(`Making GitHub API PUT request to: ${url}`);

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Authorization': `token ${GITHUB_API_TOKEN}`,
        'Content-Type': 'application/json',
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'crayolex-app'
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorText = await response.text();
      const statusCode = response.status;
      console.error(`GitHub API error (${statusCode}): ${errorText}`);

      // Check for specific errors
      if (statusCode === 401) {
        console.error('GitHub token is invalid or expired. Please generate a new token.');
      } else if (statusCode === 403) {
        console.error('GitHub API rate limit exceeded or insufficient permissions.');
      } else if (statusCode === 422) {
        console.error('Validation failed, possibly due to file conflict or invalid sha.');
      }

      return false;
    }

    console.log(`Successfully updated ${filePath} on GitHub`);
    return true;
  } catch (error) {
    console.error('Error updating file on GitHub:', error.message, error.stack);
    return false;
  }
}

// Function to store user data on GitHub with change tracking
async function storeUserData(userData) {
  try {
    console.log('Storing user data to GitHub...');

    // Get the current file content and SHA
    const fileData = await getFileFromGitHub(USER_DATA_FILE_PATH);

    if (!fileData) {
      console.error('Failed to get current file from GitHub');
      return { success: false };
    }

    let existingData = {};
    let result = {
      success: false,
      changes: false,
      added: [],
      removed: [],
      valueChanges: [],
      hasValueChanges: false
    };

    try {
      // Parse existing data from JSON
      existingData = JSON.parse(fileData.content);
      console.log('Successfully loaded existing user data');
    } catch (parseError) {
      console.warn('Error parsing existing JSON data, treating as empty:', parseError.message);
      // Continue with empty object if parsing fails
    }


    // Create filtered copies of the data for storage
    const filteredUserData = { ...userData };
    fieldsToFilterOut.forEach(field => {
      delete filteredUserData[field];
    });

    // Track field changes using the original data but exclude filtered fields from reporting
    const existingFields = new Set(Object.keys(existingData));
    const newFields = new Set(Object.keys(userData));

    // Find added fields (in new data but not in existing data)
    // But exclude fields that should be filtered out
    const addedFields = [...newFields]
      .filter(field => !existingFields.has(field))
      .filter(field => !fieldsToFilterOut.includes(field));

    // Find removed fields (in existing data but not in new data)
    // But exclude fields that should be filtered out
    const removedFields = [...existingFields]
      .filter(field => !newFields.has(field))
      .filter(field => !fieldsToFilterOut.includes(field));

    // Track value changes in existing fields
    const changedFields = [];
    for (const field of existingFields) {
      // Skip fields that should be filtered out
      if (fieldsToFilterOut.includes(field)) continue;

      if (newFields.has(field) && JSON.stringify(existingData[field]) !== JSON.stringify(userData[field])) {
        changedFields.push({
          field,
          oldValue: existingData[field],
          newValue: userData[field]
        });
      }
    }

    // Update tracking information
    result.added = addedFields;
    result.removed = removedFields;
    result.valueChanges = changedFields;
    result.hasValueChanges = changedFields.length > 0;
    result.changes = addedFields.length > 0 || removedFields.length > 0;

    // Log detailed changes
    if (addedFields.length > 0) {
      console.log(`New fields detected from Prolific: ${addedFields.join(', ')}`);
    }

    if (removedFields.length > 0) {
      console.log(`Fields removed from Prolific: ${removedFields.join(', ')}`);
    }

    if (changedFields.length > 0) {
      console.log(`Value changes detected in ${changedFields.length} fields:`);
      changedFields.forEach(change => {
        console.log(`  - ${change.field}: ${JSON.stringify(change.oldValue)} → ${JSON.stringify(change.newValue)}`);
      });
    }

    // We've already filtered userData above, so we can use filteredUserData directly

    // Update the file with filtered data from Prolific
    // This approach allows dynamic handling of field additions/removals in Prolific's API
    const updatedContent = JSON.stringify(filteredUserData, null, 4);

    // Check if data has actually changed before writing
    const hasChanged = updatedContent !== fileData.content;

    // Consider both structure changes and value changes
    const shouldUpdate = hasChanged || result.changes || result.hasValueChanges;

    if (!shouldUpdate) {
      console.log('User data unchanged, skipping update');
      result.success = true;
      return result;
    }

    console.log('User data changed, updating GitHub file');
    const success = await updateFileOnGitHub(USER_DATA_FILE_PATH, updatedContent, fileData.sha);

    result.success = success;

    // Expanded logging for change detection and email sending
    if (result.changes || result.hasValueChanges) {
      console.log(`Changes detected - should send notification email (structure changes: ${result.changes}, value changes: ${result.hasValueChanges})`);
      if (success) {
        console.log('File updated successfully - attempting to send notification email');
        try {
          // First attempt using native nodemailer
          const emailSent = await sendNotificationEmail(result.added, result.removed, result.valueChanges);
          console.log(`Notification email ${emailSent ? 'sent successfully' : 'failed to send using native nodemailer'}`);
          result.emailSent = emailSent;

          // If native email fails, try the send-email endpoint as backup
          if (!emailSent) {
            console.log('Attempting to send notification via send-email endpoint as backup...');
            try {
              // Prepare a message for the send-email endpoint with detailed changes
              const subject = 'Prolific User Data Changes Detected';
              let message = `Changes detected in Prolific user data:\n\n`;

              if (result.added.length > 0) {
                message += `Fields added: ${result.added.join(', ')}\n\n`;
              }

              if (result.removed.length > 0) {
                message += `Fields removed: ${result.removed.join(', ')}\n\n`;
              }

              // Filter out fields we don't want to report from value changes
              const realChanges = result.valueChanges.filter(change => !fieldsToFilterOut.includes(change.field));

              // If there are no reportable changes after filtering, skip sending email
              if (result.added.length === 0 && result.removed.length === 0 && realChanges.length === 0) {
                console.log('No reportable changes after filtering, skipping backup notification');
                return true; // Skip sending email
              }

              // Include detailed value changes with old and new values
              if (realChanges.length > 0) {
                message += `Fields with value changes: ${realChanges.map(c => c.field).join(', ')}\n\n`;

                // Add detailed breakdown of each change
                message += `Details of changes:\n\n`;
                realChanges.forEach(change => {
                  const oldValue = typeof change.oldValue === 'object'
                    ? JSON.stringify(change.oldValue)
                    : String(change.oldValue);
                  const newValue = typeof change.newValue === 'object'
                    ? JSON.stringify(change.newValue)
                    : String(change.newValue);

                  message += `Field: ${change.field}\n`;
                  message += `Old value: ${oldValue}\n`;
                  message += `New value: ${newValue}\n\n`;
                });
              }

              message += `Detected at: ${new Date().toISOString()}`;

              // Use fetch to call the send-email endpoint
              const emailResponse = await fetch('https://crayolex.com/api/prolific/send-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  from: '<EMAIL>',
                  to: NOTIFICATION_EMAIL,
                  subject,
                  body: message
                })
              });

              const emailResult = await emailResponse.json();
              console.log('Backup email send result:', emailResult);
              result.backupEmailSent = emailResult.success;
            } catch (backupEmailError) {
              console.error('Error sending backup email:', backupEmailError);
              result.backupEmailError = backupEmailError.message;
            }
          }
        } catch (emailError) {
          console.error('Error in sendNotificationEmail:', emailError.message, emailError.stack);
          result.emailError = emailError.message;

          // Try the backup method even if the primary method throws an error
          try {
            console.log('Error in primary email method, trying backup method...');
            // [Same backup email code as above would go here, but removed for brevity]
            // Simplified version:
            const backupResponse = await fetch('https://crayolex.com/api/prolific/send-email', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                from: '<EMAIL>',
                to: NOTIFICATION_EMAIL,
                subject: 'ERROR: Prolific Data Changes - Primary Email Failed',
                body: `Data changes detected but primary email method failed:\n${emailError.message}\n\nPlease check server logs.`
              })
            });
            const backupResult = await backupResponse.json();
            result.emergencyEmailSent = backupResult.success;
          } catch (emergencyError) {
            console.error('Both primary and emergency email methods failed:', emergencyError);
            result.allEmailMethodsFailed = true;
          }
        }
      } else {
        console.log('File update failed - skipping notification email');
      }
    } else {
      console.log('No changes detected - skipping notification email');
    }

    return result;
  } catch (error) {
    console.error('Error storing user data:', error.message, error.stack);
    return false;
  }
}

// Function to get latest user data
async function getLatestUserData() {
  try {
    // Get the file from GitHub
    const fileData = await getFileFromGitHub(USER_DATA_FILE_PATH);

    if (!fileData) {
      console.log('No user data file found on GitHub');
      return null;
    }

    try {
      const jsonData = JSON.parse(fileData.content);

      // Check if we have actual data
      if (jsonData && Object.keys(jsonData).length > 0) {
        console.log('Retrieved user data from GitHub');
        return jsonData;
      }
    } catch (parseError) {
      console.error('Error parsing JSON data:', parseError.message);
    }

    console.log('No valid user data available');
    return null;
  } catch (error) {
    console.error('Error getting latest user data:', error.message);
    return null;
  }
}

// Function to send notification email about user data changes
async function sendNotificationEmail(addedFields, removedFields, valueChanges = []) {
  try {
    console.log('Beginning sendNotificationEmail process...');
    console.log(`Changes to notify: ${addedFields.length} added fields, ${removedFields.length} removed fields, ${valueChanges.length} value changes`);

    // Filter out fields we don't want to report
    const filteredValueChanges = valueChanges.filter(change => !fieldsToFilterOut.includes(change.field));

    // If there are no changes to report after filtering, skip sending email
    if (addedFields.length === 0 && removedFields.length === 0 && filteredValueChanges.length === 0) {
      console.log('No reportable changes after filtering, skipping notification email');
      return true; // Return true to indicate success (no need to try backup method)
    }

    // Check SMTP configuration
    if (!SMTP_HOST || !SMTP_USER || !SMTP_PASSWORD || !SMTP_EMAIL) {
      console.error('SMTP configuration is incomplete. Cannot send email notification.');
      console.error(`SMTP_HOST: ${SMTP_HOST ? 'set' : 'missing'}`);
      console.error(`SMTP_USER: ${SMTP_USER ? 'set' : 'missing'}`);
      console.error(`SMTP_PASSWORD: ${SMTP_PASSWORD ? 'set' : 'missing'}`);
      console.error(`SMTP_EMAIL: ${SMTP_EMAIL ? 'set' : 'missing'}`);
      return false;
    }

    console.log('Creating email transporter with SMTP configuration...');
    // Create a transporter using SMTP configuration
    const transporter = nodemailer.createTransport({
      host: SMTP_HOST,
      port: 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: SMTP_USER,
        password: SMTP_PASSWORD
      },
      debug: true, // Enable debug output
      logger: true  // Log information to the console
    });

    console.log('Verifying transporter connection...');
    try {
      await transporter.verify();
      console.log('Transporter connection verified successfully');
    } catch (verifyError) {
      console.error('Transporter verification failed:', verifyError.message);
      throw new Error(`SMTP connection verification failed: ${verifyError.message}`);
    }

    // Format the email content with more detailed styling
    let emailContent = `
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; }
          h2 { color: #004080; border-bottom: 1px solid #004080; padding-bottom: 8px; }
          h3 { color: #004080; margin-top: 20px; }
          ul { margin-top: 10px; }
          li { margin-bottom: 5px; }
          .changes { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .timestamp { color: #666; font-size: 0.9em; margin-top: 20px; }
          .highlight { color: red; font-weight: bold; }
          .value-change { margin-bottom: 15px; border-left: 3px solid #004080; padding-left: 10px; }
          .old-value { color: #d9534f; }
          .new-value { color: #5cb85c; }
          .change-summary { font-weight: bold; }
        </style>
      </head>
      <body>
        <h2>Prolific User Data Changes Detected</h2>
        <p>Changes in the Prolific API data have been detected and require your attention.</p>
        <div class="changes">
    `;

    if (addedFields.length > 0) {
      emailContent += `<h3>New Fields Added:</h3><ul>`;
      addedFields.forEach(field => {
        emailContent += `<li><span class="highlight">${field}</span> - New field in the Prolific API data structure</li>`;
      });
      emailContent += `</ul>`;
    }

    if (removedFields.length > 0) {
      emailContent += `<h3>Fields Removed:</h3><ul>`;
      removedFields.forEach(field => {
        emailContent += `<li><span class="highlight">${field}</span> - This field is no longer present in the Prolific API</li>`;
      });
      emailContent += `</ul>`;
    }

    // Use the filtered value changes
    if (filteredValueChanges.length > 0) {
      emailContent += `<h3>Value Changes:</h3>`;
      filteredValueChanges.forEach(change => {
        const oldValueStr = typeof change.oldValue === 'object'
          ? JSON.stringify(change.oldValue, null, 2)
          : String(change.oldValue);

        const newValueStr = typeof change.newValue === 'object'
          ? JSON.stringify(change.newValue, null, 2)
          : String(change.newValue);

        emailContent += `
          <div class="value-change">
            <p class="change-summary">Field: <span class="highlight">${change.field}</span></p>
            <p>Old value: <span class="old-value">${oldValueStr}</span></p>
            <p>New value: <span class="new-value">${newValueStr}</span></p>
          </div>
        `;
      });
    }

    emailContent += `
        </div>
        <p>These changes may require updates to your processing code to handle the new data.</p>
        <p class="timestamp">Detected at: ${new Date().toISOString()}</p>
        <p>Please check your repository for the updated data.</p>
      </body>
      </html>
    `;

    console.log('Preparing to send notification email...');
    console.log(`To: ${NOTIFICATION_EMAIL}`);

    // Send the email with more detailed subject
    let subject = '🚨 Prolific API Data Change Detected';
    if (addedFields.length > 0 || removedFields.length > 0) {
      subject = `🚨 Prolific API Structure Change - ${addedFields.length} Added, ${removedFields.length} Removed`;
    } else if (valueChanges.length > 0) {
      subject = `🚨 Prolific API Value Changes - ${valueChanges.length} Fields Updated`;
    }

    console.log(`Sending email with subject: ${subject}`);
    const info = await transporter.sendMail({
      from: `"Prolific Data Monitor" <${SMTP_EMAIL}>`,
      to: NOTIFICATION_EMAIL,
      subject: subject,
      html: emailContent
    });

    console.log(`Email notification sent: ${info.messageId}`);
    console.log(`Email preview URL: ${nodemailer.getTestMessageUrl(info)}`);
    return true;
  } catch (error) {
    console.error('Error sending notification email:', error.message);
    console.error('Error stack:', error.stack);

    // Try to send a fallback email about the failure
    try {
      if (SMTP_HOST && SMTP_USER && SMTP_PASSWORD && SMTP_EMAIL) {
        const fallbackTransporter = nodemailer.createTransport({
          host: SMTP_HOST,
          port: 587,
          secure: false,
          auth: {
            user: SMTP_USER,
            password: SMTP_PASSWORD
          }
        });

        await fallbackTransporter.sendMail({
          from: `"Prolific Data Monitor" <${SMTP_EMAIL}>`,
          to: NOTIFICATION_EMAIL,
          subject: "ERROR: Failed to send Prolific data change notification",
          html: `
            <h2>Error Sending Data Change Notification</h2>
            <p>An error occurred while trying to send a notification about Prolific data changes:</p>
            <pre>${error.message}\n${error.stack}</pre>
            <p>Added fields: ${addedFields.join(', ') || 'none'}</p>
            <p>Removed fields: ${removedFields.join(', ') || 'none'}</p>
            <p>Changed values: ${valueChanges.length > 0 ? valueChanges.map(c => c.field).join(', ') : 'none'}</p>
          `
        });
        console.log('Sent fallback error notification email');
      }
    } catch (fallbackError) {
      console.error('Failed to send fallback error notification:', fallbackError.message);
    }

    return false;
  }
}

// GET handler
export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action');

  if (action === 'validate_token') {
    const tokenStatus = await validateGitHubToken();
    return NextResponse.json({
      success: tokenStatus.valid,
      valid: tokenStatus.valid,
      message: tokenStatus.valid ? 'GitHub token is valid' : `GitHub token is invalid: ${tokenStatus.reason}`,
      reason: tokenStatus.reason || null
    });
  } else if (action === 'get_user_data') {
    const userData = await getLatestUserData();

    return NextResponse.json({
      success: !!userData,
      userData: userData,
      message: userData ? 'Latest user data retrieved' : 'No user data available',
      source: 'github_api'
    });
  }

  return NextResponse.json({ success: false, message: 'Invalid action' }, { status: 400 });
}

// POST handler
export async function POST(request) {
  try {
    // Parse the JSON body
    const data = await request.json();
    const action = data.action;

    console.log('Received POST data:', data);

    // Check token validity for operations that require GitHub access
    if (action === 'store_user_data') {
      const tokenStatus = await validateGitHubToken();
      if (!tokenStatus.valid) {
        return NextResponse.json({
          success: false,
          message: `GitHub authentication failed: ${tokenStatus.reason}. Cannot update JSON file without valid token.`,
          tokenValid: false,
          tokenError: tokenStatus.reason
        }, { status: 401 });
      }
    }

    // Only handle user data storage with specific action name
    if (action === 'store_user_data') {
      console.log('Storing user data for processing:', data.userData);

      if (!data.userData) {
        console.warn('Missing userData in request');
        return NextResponse.json({
          success: false,
          message: 'Missing userData in request'
        }, { status: 400 });
      }

      // Store the user data and track field changes
      const result = await storeUserData(data.userData);
      // Return enhanced information with value changes and email status
      return NextResponse.json({
        success: result.success,
        message: result.success ? 'User data stored successfully' : 'Failed to store user data',
        fileWriteSuccess: result.success,
        storage: 'github_api',
        dataChanges: {
          structureChanges: result.changes || false,
          valueChanges: result.hasValueChanges || false,
          totalChanges: (result.changes || result.hasValueChanges) || false
        },
        fieldsAdded: result.added || [],
        fieldsRemoved: result.removed || [],
        // Filter out fields from value changes reported to client
        fieldsWithValueChanges: result.valueChanges
            ? result.valueChanges
                .filter(change => !fieldsToFilterOut.includes(change.field)) // Use the shared array
                .map(change => change.field) :
            [],
        valueChangeCount: result.valueChanges
            ? result.valueChanges.filter(change => !fieldsToFilterOut.includes(change.field)).length // Use the shared array
            :
            0,
        emailSent: result.emailSent || false,
        emailError: result.emailError || null,
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json({
      success: false,
      message: 'Invalid action'
    }, { status: 400 });
  } catch (error) {
    console.error('Error processing request:', error);
    return NextResponse.json({
      success: false,
      message: 'Error processing request: ' + error.message
    }, { status: 500 });
  }
}