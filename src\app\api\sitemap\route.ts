import { NextResponse } from 'next/server';

const baseUrl = 'https://crayolex.com';

export async function GET() {
  // Get all the routes in the app
  const routes = [
    '',
    '/about',
    '/ai/ai-edits',
    '/ai/faq',
    '/ai/submit-edit',
    '/ai/tos',
    '/blog',
    '/blog-details',
    '/blog-sidebar',
    '/contact',
    '/privacy-policy',
    '/signin',
    '/signup'
  ];

  // Generate the XML sitemap
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${routes
    .map((route) => {
      return `
  <url>
    <loc>${baseUrl}${route}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${route === '' ? '1.0' : '0.8'}</priority>
  </url>`;
    })
    .join('')}
</urlset>`;

  // Return the XML response
  return new NextResponse(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=86400, s-maxage=86400'
    }
  });
}