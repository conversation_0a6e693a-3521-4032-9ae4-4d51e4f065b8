"use client";

import Footer from "@/components/Footer";
import Header from "@/components/Header";
import ScrollToTop from "@/components/ScrollToTop";
import { <PERSON>, <PERSON><PERSON><PERSON>, Orbitron } from "next/font/google"; // Import Orbitron
import "../styles/index.css";
import Script from 'next/script';
import Hotjar from '@/components/Analytics/Hotjar';
import { Providers } from "./providers";

const inter = Inter({ subsets: ["latin"] });
const rajdhani = <PERSON><PERSON><PERSON>({ 
  weight: ['300', '400', '500', '600', '700'],
  subsets: ["latin"],
  variable: '--font-rajdhani'
});

// Configure Orbitron
const orbitron = Orbitron({
  weight: ['400', '500', '600', '700', '800', '900'], // Adjust weights as needed
  subsets: ['latin'],
  display: 'swap', // Optional: Improves font loading performance
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html suppressHydrationWarning lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="description" content="Explore engaging crosswords &amp; word searches. Enjoy whimsical, rhyming educational stories that will captivate and educate young minds." />
        <meta name="keywords" content="album design, photography, wedding albums, event photography, professional albums, digital albums" />
        <meta name="robots" content="index, follow" />
        <title>Fun Puzzles &amp; Children&apos;s Books for Eager Learners</title>
        <Script src="https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit" async defer />
        <Hotjar />
      </head>

      <body className={`flex flex-col min-h-screen bg-[#FCFCFC] dark:bg-black ${rajdhani.className} ${rajdhani.variable}`}> {/* Use rajdhani.className as base */}
        <Providers>
          <Header />
          <div className="h-px w-full bg-gradient-to-r from-transparent via-[#D2D8E183] to-transparent dark:via-[#959CB183]"></div>
          <main className="flex-grow">{children}</main>
          <Footer />
          <ScrollToTop />
        </Providers>
        {/* Portal target for modals */}
        <div id="modal-root"></div>
      </body>
    </html>
  );
}
