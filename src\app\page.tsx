import ScrollUp from "@/components/Common/ScrollUp";
import Contact from "@/components/Contact";
import Hero from "@/components/Hero";
import FeaturedBook from "@/components/FeaturedBook";
import Books from "@/components/Books";
import Testimonials from "@/components/Testimonials";
import Acknowledgments from '@/components/Acknowledgments';
import { Metadata } from "next";
import Footer from "@/components/Footer";

export const metadata: Metadata = {
  title: "Crayolex Books and Puzzles",
  description: "Explore engaging crosswords & word searches. Enjoy whimsical, rhyming educational stories.",
  // other metadata
};

export default function Home() {
  return (
    <>
      <ScrollUp />
      <Hero />
      <FeaturedBook />
      <Books />
      <Testimonials />
      <Acknowledgments />
      <Contact />
    </>
  );
}
