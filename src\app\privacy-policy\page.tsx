import Breadcrumb from "@/components/Common/Breadcrumb";
import React from 'react';
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Privacy Policy | Crayolex",
  description: "Read the Crayolex Privacy Policy.",
  // other metadata
};


const PrivacyPolicyPage = () => {
  return (
    <>
      <Breadcrumb
        pageName="Privacy Policy"
        description="Read the Crayolex Privacy Policy."
      />
      <section className="pt-16 pb-16">
        <div className="container">
          <div className="-mx-4 flex flex-wrap justify-center">
            <div className="w-full px-4 lg:w-8/12"> {/* Use similar width as About page */}
              <div className="prose prose-p:text-base lg:prose-p:text-lg dark:prose-invert max-w-none"> {/* Added prose classes & responsive p size */}
                <h1 className="text-3xl font-bold mb-6">Privacy Policy</h1>

                <section className="mb-6">
                  <h2 className="text-2xl font-semibold mb-3">Who we are</h2>
                  <p>Our website address is: https://crayolex.com.</p>
                </section>

                <section className="mb-6">
                  <h2 className="text-2xl font-semibold mb-3">Comments</h2>
                  <p className="mb-2">
                    When visitors leave comments on the site we collect the data shown in the comments form, and also the visitor’s IP address and browser user agent string to help spam detection.
                  </p>
                  <p className="mb-2">
                    An anonymized string created from your email address (also called a hash) may be provided to the Gravatar service to see if you are using it. The Gravatar service privacy policy is available here: <a href="https://automattic.com/privacy/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">https://automattic.com/privacy/</a>. After approval of your comment, your profile picture is visible to the public in the context of your comment.
                  </p>
                  <p>As an Amazon Associate, I earn from qualifying purchases.</p>
                </section>

                <section className="mb-6">
                  <h2 className="text-2xl font-semibold mb-3">Cookies</h2>
                  <p className="mb-2">
                    If you leave a comment on our site you may opt-in to saving your name, email address and website in cookies. These are for your convenience so that you do not have to fill in your details again when you leave another comment. These cookies will last for one year.
                  </p>
                  <p className="mb-2">
                    If you visit our login page, we will set a temporary cookie to determine if your browser accepts cookies. This cookie contains no personal data and is discarded when you close your browser.
                  </p>
                  <p className="mb-2">
                    When you log in, we will also set up several cookies to save your login information and your screen display choices. Login cookies last for two days, and screen options cookies last for a year. If you select “Remember Me”, your login will persist for two weeks. If you log out of your account, the login cookies will be removed.
                  </p>
                  <p>
                    If you edit or publish an article, an additional cookie will be saved in your browser. This cookie includes no personal data and simply indicates the post ID of the article you just edited. It expires after 1 day.
                  </p>
                </section>

                <section className="mb-6">
                  <h2 className="text-2xl font-semibold mb-3">Who we share your data with</h2>
                  <p>If you request a password reset, your IP address will be included in the reset email.</p>
                </section>

                <section className="mb-6">
                  <h2 className="text-2xl font-semibold mb-3">How long we retain your data</h2>
                  <p className="mb-2">
                    If you leave a comment, the comment and its metadata are retained indefinitely. This is so we can recognize and approve any follow-up comments automatically instead of holding them in a moderation queue.
                  </p>
                  <p>
                    For users that register on our website (if any), we also store the personal information they provide in their user profile. All users can see, edit, or delete their personal information at any time (except they cannot change their username). Website administrators can also see and edit that information.
                  </p>
                </section>

                <section className="mb-6">
                  <h2 className="text-2xl font-semibold mb-3">What rights you have over your data</h2>
                  <p className="mb-2">
                    If you have an account on this site, or have left comments, you can request to receive an exported file of the personal data we hold about you, including any data you have provided to us. You can also request that we erase any personal data we hold about you. This does not include any data we are obliged to keep for administrative, legal, or security purposes.
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold mb-3">Where we send your data</h2>
                  <p>Visitor comments may be checked through an automated spam detection service.</p>
                </section>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default PrivacyPolicyPage;