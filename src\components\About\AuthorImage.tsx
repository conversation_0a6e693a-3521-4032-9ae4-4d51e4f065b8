"use client"; // This component needs client-side interactivity

import { useState, useRef, MouseEvent } from "react";
import Image from "next/image";

interface AuthorImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
}

const AuthorImage: React.FC<AuthorImageProps> = ({ src, alt, width, height }) => {
  // --- State and Ref for hover effects ---
  const mainContainerRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);

  // --- Event Handlers ---
  const handleMouseMove = (e: MouseEvent<HTMLDivElement>) => {
    if (!mainContainerRef.current || isFocused) return;
    const div = mainContainerRef.current;
    const rect = div.getBoundingClientRect();
    setPosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });
  };

  const handleFocus = () => {
    setIsFocused(true);
    setOpacity(1);
  };

  const handleBlur = () => {
    setIsFocused(false);
    setOpacity(0);
  };

  const handleMouseEnter = () => {
    setOpacity(1);
  };

  const handleMouseLeave = () => {
    setOpacity(0);
  };

  return (
    <div
      ref={mainContainerRef}
      onMouseMove={handleMouseMove}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className="group relative mb-10 inline-block border border-tokyo-red/50 dark:border-tokyo-turquoise/50 rounded-full p-2 shadow-lg hover:border-tokyo-turquoise dark:hover:border-tokyo-turquoise hover:shadow-neon-intense transition-all duration-200 overflow-hidden mx-auto" // Changed rounded-lg to rounded-full
      tabIndex={0} // Make it focusable
    >
      {/* Spotlight Effect Div */}
      <div
        className="pointer-events-none absolute -inset-px opacity-0 transition duration-300 group-hover:opacity-100 z-10"
        style={{
          opacity,
          background: `radial-gradient(600px circle at ${position.x}px ${position.y}px, rgba(3, 169, 144, 0.1), transparent 40%)`, // Turquoise glow
        }}
      />
      {/* Image - Added relative z-0 */}
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        className="rounded-full object-cover relative z-0" // Changed rounded-lg to rounded-full
        priority // Prioritize loading the author image
      />
    </div>
  );
};

export default AuthorImage;