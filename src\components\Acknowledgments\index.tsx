import React from 'react';

const Acknowledgments = () => {
  return (
    <section className="py-16 bg-white dark:bg-gray-dark"> {/* Added light mode bg */}
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-orbitron font-bold text-black dark:text-white red-text-glow mb-4"> {/* Added light mode text */}
            Special Thanks
          </h2>
          <p className="text-body-color dark:text-body-color dark:text-body-color-dark font-rajdhani text-2xl"> {/* Increased text size */}
            To my wonderful family and friends who contributed and helped make these books possible.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="p-6 bg-white dark:bg-gray-dark-secondary rounded-lg shadow-neon duration-300 hover:shadow-neon-intense">
            <h3 className="text-xl font-orbitron text-tokyo-turquoise mb-2">Dr<PERSON> <PERSON><PERSON>, PhD</h3>
            <p className="text-body-color dark:text-body-color-dark font-rajdhani">My father</p>
          </div>
          <div className="p-6 bg-white dark:bg-gray-dark-secondary rounded-lg shadow-neon duration-300 hover:shadow-neon-intense">
            <h3 className="text-xl font-orbitron text-tokyo-turquoise mb-2">Shiffy Reisman, MIT</h3>
            <p className="text-body-color dark:text-body-color-dark font-rajdhani">My precious daughter</p>
          </div>
          <div className="p-6 bg-white dark:bg-gray-dark-secondary rounded-lg shadow-neon duration-300 hover:shadow-neon-intense">
            <h3 className="text-xl font-orbitron text-tokyo-turquoise mb-2">Dr. Melvin Z. Twersky, DO</h3>
            <p className="text-body-color dark:text-body-color-dark font-rajdhani">My uncle</p>
          </div>
          <div className="p-6 bg-white dark:bg-gray-dark-secondary rounded-lg shadow-neon duration-300 hover:shadow-neon-intense">
            <h3 className="text-xl font-orbitron text-tokyo-turquoise mb-2">Sholom J. Twersky, JD</h3>
            <p className="text-body-color dark:text-body-color-dark font-rajdhani">My uncle</p>
          </div>
          <div className="p-6 bg-white dark:bg-gray-dark-secondary rounded-lg shadow-neon duration-300 hover:shadow-neon-intense">
            <h3 className="text-xl font-orbitron text-tokyo-turquoise mb-2">Dr. Shoshana S. Twersky, PsyD</h3>
            <p className="text-body-color dark:text-body-color-dark font-rajdhani">My dear sister</p>
          </div>
          <div className="p-6 bg-white dark:bg-gray-dark-secondary rounded-lg shadow-neon duration-300 hover:shadow-neon-intense">
            <h3 className="text-xl font-orbitron text-tokyo-turquoise mb-2">Dr. Doni Zivotofsky, DVM</h3>
            <p className="text-body-color dark:text-body-color-dark font-rajdhani">Our family vet</p>
          </div>
          <div className="p-6 bg-white dark:bg-gray-dark-secondary rounded-lg shadow-neon duration-300 hover:shadow-neon-intense">
            <h3 className="text-xl font-orbitron text-tokyo-turquoise mb-2">Dr. John Shavel, DMD</h3>
            <p className="text-body-color dark:text-body-color-dark font-rajdhani">My step-uncle</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Acknowledgments;
