"use client";
import { useRef, useState, MouseEvent } from "react";
import Image from "next/image"; // Keep for Amazon icons etc.
import ImageZoom from "@/components/ImageZoom"; // Import ImageZoom
import type { BookType } from "@/data/books";

interface BookCardProps {
  book: BookType;
  hideImage?: boolean;
  className?: string;
  disableHoverEffects?: boolean;
}

const BookCard = ({
  book,
  hideImage = false,
  className = "",
  disableHoverEffects = false
}: BookCardProps) => {
  const divRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);

  const handleMouseMove = (e: MouseEvent<HTMLDivElement>) => {
    if (!divRef.current || isFocused || disableHoverEffects) return;
    const div = divRef.current;
    const rect = div.getBoundingClientRect();
    setPosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });
  };

  const handleFocus = () => {
    if (disableHoverEffects) return;
    setIsFocused(true);
    setOpacity(1);
  };

  const handleBlur = () => {
    if (disableHoverEffects) return;
    setIsFocused(false);
    setOpacity(0);
  };

  const handleMouseEnter = () => {
    if (disableHoverEffects) return;
    setOpacity(1);
  };

  const handleMouseLeave = () => {
    if (disableHoverEffects) return;
    setOpacity(0);
  };

  const hoverClasses = !disableHoverEffects
    ? "group hover:border-tokyo-turquoise dark:hover:border-tokyo-turquoise hover:shadow-neon-intense"
    : "";

  // Common text color classes with hover effect
  const textColorClasses = `text-body-color dark:text-body-color-dark transition-colors duration-200 ${!disableHoverEffects ? 'group-hover:text-black dark:group-hover:text-white' : ''}`;

  return (
    <div
      ref={divRef}
      onMouseMove={!disableHoverEffects ? handleMouseMove : undefined}
      onFocus={!disableHoverEffects ? handleFocus : undefined}
      onBlur={!disableHoverEffects ? handleBlur : undefined}
      onMouseEnter={!disableHoverEffects ? handleMouseEnter : undefined}
      onMouseLeave={!disableHoverEffects ? handleMouseLeave : undefined}
      className={`relative rounded-lg bg-gray-light dark:bg-gray-dark border border-stroke-stroke dark:border-gray-700 transition-all duration-200 flex flex-col h-full overflow-hidden p-4 ${hoverClasses} ${className}`} // Added p-4 for consistent padding
    >
      {/* Spotlight Effect Div - Placed early to be under content */}
      {!disableHoverEffects && (
        <div
          className="pointer-events-none absolute -inset-px opacity-0 transition duration-300 group-hover:opacity-100"
          style={{
            opacity,
            // Adjusted gradient to cover the padded area correctly
            background: `radial-gradient(600px circle at ${position.x}px ${position.y}px, rgba(3, 169, 144, 0.1), transparent 40%)`,
          }}
        />
      )}

      {/* 1. Image Section */}
      {!hideImage && (
        <div className="mb-2 w-full"> {/* Small margin below image */}
          <ImageZoom
            src={book.imageUrl}
            alt={book.title}
            width={300} // Maintain aspect ratio if possible
            height={450}
            className="w-full h-auto object-cover rounded-md" // Fill width, maintain aspect, cover area
          />
        </div>
      )}

      {/* 2. Title and Description Section */}
      <div className={`mb-3 w-full ${hideImage ? 'mt-0' : ''}`}> {/* Margin below title/desc */}
        <h3 className={`text-3xl font-semibold mb-1 text-left ${textColorClasses}`}>{book.title}</h3>
        <p className={`text-lg text-left leading-normal ${textColorClasses}`}>{book.description}</p>
      </div>

      {/* Grid for remaining sections (Page #, Rating, Price, Button) */}
      <div className="grid grid-cols-1 xl:grid-cols-2 lg:grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2 mt-auto w-full"> {/* Responsive grid that stacks at lg breakpoint */}

        {/* 3. Page Number Section (Top Left of grid area) */}
        <div className="justify-self-start text-left">
          <p className={`text-base ${textColorClasses}`}>Pages: <span className="font-bold">{book.pageCount}</span></p>
        </div>

        {/* 4. Star Rating Section (Top Right of grid area) */}
        <div className="justify-self-end text-right">
          <div className="flex items-center justify-end">
             <Image src="/images/books/icon-amazon-dark.png" alt="Amazon Rating" width={16} height={16} className="mr-1 w-4 h-4 block dark:hidden" />
             <Image src="/images/books/icon-amazon-light.png" alt="Amazon Rating" width={16} height={16} className="mr-1 w-4 h-4 hidden dark:block" />
            <div className="flex items-center mr-1">
              {[...Array(5)].map((_, i) => {
                const ratingFraction = book.rating - i;
                let fillPercent = 0;
                if (ratingFraction >= 0.75) fillPercent = 100;
                else if (ratingFraction >= 0.25) fillPercent = 50;
                else fillPercent = 0;
                if (ratingFraction >= 1) fillPercent = 100;
                else if (fillPercent < 0) fillPercent = 0; // Corrected condition
                const gradientId = `starGradient-${book.id}-${i}`;
                return (
                  <svg key={i} className="w-4 h-4" fill={`url(#${gradientId})`} viewBox="0 0 20 20">
                    <defs>
                      <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style={{ stopColor: '#FF9900', stopOpacity: 1 }} />
                        <stop offset={`${fillPercent}%`} style={{ stopColor: '#FF9900', stopOpacity: 1 }} />
                        <stop offset={`${fillPercent}%`} style={{ stopColor: 'rgb(156 163 175)', stopOpacity: 1 }} /> {/* Use Tailwind gray or similar */}
                        <stop offset="100%" style={{ stopColor: 'rgb(156 163 175)', stopOpacity: 1 }} />
                      </linearGradient>
                    </defs>
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                );
              })}
            </div>
            <span className={`text-base font-medium ${textColorClasses}`}>
               ({book.reviews}) {/* Removed "reviews" text */}
            </span>
          </div>
        </div>

        {/* 5a. Price Section (Bottom Left of grid area) */}
        <div className="justify-self-start text-left mb-2 xl:mb-0 lg:mb-2 md:mb-0">
          <span className={`text-xl font-semibold ${textColorClasses}`}>{book.price}</span>
        </div>

        {/* 5b. Button Section (Bottom Right of grid area) */}
        <div className="justify-self-start xl:justify-self-end lg:justify-self-start md:justify-self-end text-left xl:text-right lg:text-left md:text-right">
          <a
            href={book.amazonLink}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center justify-center gap-1 bg-amazon-orange text-white rounded hover:bg-orange-600 hover:shadow-[0_0_10px_rgba(217,108,61,0.5)] transition-all duration-200 text-base px-4 xl:px-8 lg:px-4 md:px-6 py-2 whitespace-nowrap" // Responsive padding
          >
            <Image src="/images/books/icon-amazon-light.png" alt="" width={16} height={16} className="w-4 h-4 block dark:hidden" />
            <Image src="/images/books/icon-amazon-dark.png" alt="" width={16} height={16} className="w-4 h-4 hidden dark:block" />
            Buy on Amazon
          </a>
        </div>
      </div>
    </div>
  );
};

export default BookCard;
