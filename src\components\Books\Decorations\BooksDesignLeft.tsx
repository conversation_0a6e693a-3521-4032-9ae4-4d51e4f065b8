import React from 'react';

interface BooksDesignLeftProps {
  className?: string;
}

const tealColor = '#38B2AC'; // Base teal color
const lightTeal = '#4FD1C5'; // Lighter teal for variation
const darkTeal = '#2C7A7B'; // Darker teal for depth

const BooksDesignLeft: React.FC<BooksDesignLeftProps> = ({ className }) => {
  return (
    <svg
      className={className}
      // Keep adjusted viewBox allowing for off-screen elements
      viewBox="-20 -100 140 960"
      preserveAspectRatio="none"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="fadeGradientLeftWeaveFinalAgain" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="10%" style={{ stopColor: tealColor, stopOpacity: 1 }} />
          <stop offset="90%" style={{ stopColor: tealColor, stopOpacity: 1 }} />
          <stop offset="100%" style={{ stopColor: tealColor, stopOpacity: 0 }} />
        </linearGradient>
        <filter id="subtleGlowWeaveFinalAgain" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="2.5" result="coloredBlur" />
          <feMerge>
            <feMergeNode in="coloredBlur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      {/* Apply gradient and filter */}
      <g style={{ stroke: 'url(#fadeGradientLeftWeaveFinalAgain)', fill: 'url(#fadeGradientLeftWeaveFinalAgain)' }} filter="url(#subtleGlowWeaveFinalAgain)">

        {/* --- Path 1 (Main Segmented) --- */}
        {/* Start further left (x=-20), strong horizontal control point first */}
        <g opacity="0.9">
          {/* Segment 1 - REALLY force horizontal entry */}
          {/* M -20 -80: Start way off left and high */}
          {/* C 10 -80: First control point is horizontal (same y), pulling right */}
          {/*   20 -60: Second control point starts downward curve */}
          {/*   35 -40: End point of first curve */}
          <path d="M -20 -80 C 10 -80, 20 -60, 35 -40" stroke={lightTeal} strokeWidth="1.5" />
          <ellipse cx="35" cy="-40" rx="12" ry="8" fill={tealColor} transform="rotate(-5 35 -40)" />
          {/* Connecting Line 1 */}
          <path d="M 35 -32 C 50 -10, 70 20, 85 60" stroke={tealColor} strokeWidth="0.8" />
          {/* Segment 2 */}
          <path d="M 85 60 C 100 80, 95 110, 80 140" stroke={lightTeal} strokeWidth="1.5" />
          <ellipse cx="80" cy="140" rx="15" ry="10" fill={darkTeal} transform="rotate(20 80 140)" />
          <circle cx="83" cy="138" r="3" fill={lightTeal} />
          {/* Connecting Line 2 */}
          <path d="M 80 150 C 60 180, 30 220, 20 270" stroke={tealColor} strokeWidth="1" />
          {/* Segment 3 */}
          <path d="M 20 270 C 10 290, 15 320, 30 350" stroke={lightTeal} strokeWidth="1.5" />
          <ellipse cx="30" cy="350" rx="10" ry="14" fill={tealColor} transform="rotate(-5 30 350)" />
          {/* Connecting Line 3 */}
          <path d="M 30 364 C 45 390, 70 430, 90 480" stroke={darkTeal} strokeWidth="0.7" />
          {/* Segment 4 */}
          <path d="M 90 480 C 105 500, 100 530, 85 560" stroke={lightTeal} strokeWidth="1.5" />
          <ellipse cx="85" cy="560" rx="18" ry="12" fill={tealColor} transform="rotate(15 85 560)" />
          <circle cx="80" cy="565" r="4" fill={lightTeal} />
          {/* Connecting Line 4 */}
          <path d="M 85 572 C 65 600, 35 640, 25 690" stroke={tealColor} strokeWidth="0.9" />
          {/* Segment 5 */}
          <path d="M 25 690 C 15 710, 20 740, 35 770" stroke={lightTeal} strokeWidth="1.5" />
          <ellipse cx="35" cy="770" rx="14" ry="9" fill={darkTeal} transform="rotate(-10 35 770)" />
          {/* Fading out line */}
           <path d="M 35 779 C 50 800, 70 820, 80 850" stroke={tealColor} strokeWidth="0.6" opacity="0.5" />
        </g>

        {/* --- Path 2 (Weaving Dashed Line) --- */}
        {/* Start further left (x=-10), strong horizontal control point first */}
         <g opacity="0.7">
           <path
            d="M -10 -90 Q 30 -90, 10 40 C -20 170, 60 220, 40 370 S 0 520, 50 620 C 80 700, 60 760, 70 840" // Adjusted start/control points
            stroke={lightTeal}
            strokeWidth="1.2"
            strokeDasharray="5 5"
            fill="none"
          />
          {/* Small dots along Path 2 */}
          <circle cx="10" cy="40" r="3" fill={lightTeal} />
          <circle cx="40" cy="370" r="4" fill={lightTeal} />
          <circle cx="50" cy="620" r="3" fill={lightTeal} />
         </g>

        {/* Extra floating elements (adjusted positions slightly) */}
         <circle cx="60" cy="-20" r="2" fill={lightTeal} opacity="0.4" />
         <circle cx="15" cy="180" r="3" fill={darkTeal} opacity="0.5" />
         <circle cx="95" cy="310" r="2.5" fill={tealColor} opacity="0.3" />
         <circle cx="50" cy="540" r="3" fill={lightTeal} opacity="0.4" />
         <circle cx="70" cy="740" r="2" fill={darkTeal} opacity="0.2" />

      </g>
    </svg>
  );
};

export default BooksDesignLeft;