import React from 'react';

interface BooksDesignRightProps {
  className?: string;
}

const baseRed = '#FF3B5C'; // Original red color
const lightRed = '#FF6F87'; // Lighter shade
const darkRed = '#D92B4A'; // Darker shade

// Renamed component for consistency
const BooksDesignRight: React.FC<BooksDesignRightProps> = ({ className }) => (
  <svg
    // Expanded viewBox: x starts at -150, width increased to 388 (238 + 150)
    // Allows paths to extend 150 units further left than original left edge (x=0)
    viewBox="-150 0 388 531"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    preserveAspectRatio="none" // Allow stretching
  >
    <defs>
      {/* Gradient fading right-to-left */}
      <linearGradient
        id="rightDesignFade"
        x1="100%" // Starts at the far right of the viewBox
        y1="50%"
        x2="0%"   // Fades towards the far left of the viewBox
        y2="50%"
        // gradientUnits="userSpaceOnUse" // Use userSpaceOnUse for better control with modified viewBox
      >
        <stop stopColor={baseRed} stopOpacity="0.8" /> {/* Start visible (Right) */}
        <stop offset="0.7" stopColor={baseRed} stopOpacity="0.8" /> {/* Stay visible longer */}
        <stop offset="1" stopColor={baseRed} stopOpacity="0" /> {/* Fade out (Left) */}
      </linearGradient>
       {/* Subtle glow filter */}
       <filter id="redGlow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="1.5" result="coloredBlur" />
          <feMerge>
            <feMergeNode in="coloredBlur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
    </defs>

    {/* Apply gradient and filter to a group */}
    <g stroke="url(#rightDesignFade)" filter="url(#redGlow)">
      {/* Path 1 (Thicker, Base) - Starts far right (x=238), extends far left (x=-100) */}
      <path
        d="M 238 50 C 180 80, 150 20, 100 100 C 50 180, 20 150, -50 250 C -100 350, -80 400, -120 500"
        strokeWidth="3"
        fill="none"
        stroke={baseRed} // Use base color directly for main lines
      />

      {/* Path 2 (Thinner, Offset) - Starts right, extends left */}
      <path
        d="M 238 100 C 200 130, 170 90, 120 150 C 70 210, 40 180, -20 280 C -70 380, -50 430, -100 520"
        strokeWidth="1.5"
        fill="none"
        stroke={lightRed} // Lighter shade
        opacity="0.8"
      />

      {/* Path 3 (Dashed, Different Curve) - Starts right, extends left */}
      <path
        d="M 238 150 C 190 180, 160 140, 110 200 C 60 260, 30 230, -30 330 C -80 430, -60 480, -110 531"
        strokeWidth="1"
        fill="none"
        stroke={darkRed} // Darker shade
        strokeDasharray="6 6"
        opacity="0.7"
      />

       {/* Path 4 (More Wavy, Mid) - Starts right, extends left */}
      <path
        d="M 238 250 C 210 280, 180 240, 140 300 C 100 360, 70 330, 20 400 C -20 470, 0 500, -50 531"
        strokeWidth="2"
        fill="none"
        stroke={lightRed}
        opacity="0.9"
      />

       {/* Path 5 (Subtle, Background) - Starts right, extends left */}
      <path
        d="M 238 350 C 200 380, 170 340, 130 400 C 90 460, 60 430, 10 500 C -30 570, -10 531, -60 531" // Ends within viewbox bottom
        strokeWidth="0.8"
        fill="none"
        stroke={baseRed}
        opacity="0.5"
      />
    </g>
  </svg>
);

export default BooksDesignRight; // Export with the correct name