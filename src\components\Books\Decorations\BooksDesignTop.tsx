import React from "react";

interface TopChapterMarksSVGProps {
  className?: string;
}

// Base design identical to ChapterMarksSVG, will be mirrored via CSS transform
const TopChapterMarksSVG: React.FC<TopChapterMarksSVGProps> = ({ className }) => (
  <svg
    width="100%"
    height="120" // Keep original height
    viewBox="0 0 1440 120"
    preserveAspectRatio="none"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={`w-full h-[120px] ${className}`} // Keep original height class
  >
    {/* Paths identical to ChapterMarksSVG */}
    <path
      d="M0 120L1440 0"
      stroke="url(#paint0_top_chapter_base)" // New ID
      strokeWidth="2"
      strokeDasharray="20 10" // Original dash
    />
    <path
      d="M0 100L1440 20"
      stroke="url(#paint1_top_chapter_base)" // New ID
      strokeWidth="2"
      strokeDasharray="15 15" // Original dash
    />
    <path
      d="M0 80L1440 40"
      stroke="url(#paint2_top_chapter_base)" // New ID
      strokeWidth="2"
      strokeDasharray="10 20" // Original dash
    />
    <path
      d="M0 60L1440 60"
      stroke="url(#paint3_top_chapter_base)" // New ID
      strokeWidth="2"
      strokeDasharray="5 25" // Original dash
    />
    <defs>
      {/* Gradients identical to ChapterMarksSVG, but with new IDs */}
      <linearGradient
        id="paint0_top_chapter_base" // New ID
        x1="720"
        y1="120"
        x2="720"
        y2="0"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FF3B5C" />
        <stop offset="0.5" stopColor="#00FFCC" />
        <stop offset="1" stopColor="#FF3B5C" stopOpacity="0" />
      </linearGradient>
      <linearGradient
        id="paint1_top_chapter_base" // New ID
        x1="720"
        y1="100"
        x2="720"
        y2="20"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FF3B5C" stopOpacity="0.8" />
        <stop offset="0.5" stopColor="#00FFCC" stopOpacity="0.8" />
        <stop offset="1" stopColor="#FF3B5C" stopOpacity="0" />
      </linearGradient>
      <linearGradient
        id="paint2_top_chapter_base" // New ID
        x1="720"
        y1="80"
        x2="720"
        y2="40"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FF3B5C" stopOpacity="0.6" />
        <stop offset="0.5" stopColor="#00FFCC" stopOpacity="0.6" />
        <stop offset="1" stopColor="#FF3B5C" stopOpacity="0" />
      </linearGradient>
      <linearGradient
        id="paint3_top_chapter_base" // New ID
        x1="720"
        y1="60"
        x2="720"
        y2="60"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FF3B5C" stopOpacity="0.4" />
        <stop offset="0.5" stopColor="#00FFCC" stopOpacity="0.4" />
        <stop offset="1" stopColor="#FF3B5C" stopOpacity="0" />
      </linearGradient>
    </defs>
  </svg>
);

export default TopChapterMarksSVG;