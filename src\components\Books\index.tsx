"use client";

// Removed useState import as it's no longer needed
import BookCard from "./BookCard";
import { books } from "@/data/books";
// Removed ImageZoom import as it's handled within BookCard
import BooksDesignRight from "./Decorations/BooksDesignRight";
import BooksDesignLeft from "./Decorations/BooksDesignLeft";
import BooksDesignBottom from "./Decorations/BooksDesignBottom";
import BooksDesignTop from "./Decorations/BooksDesignTop";


const Books = () => {
  // Removed selectedImage state and handlers (handleImageClick, closeZoom)

  // Filter for puzzle books (assuming ID 1 is the featured children's book)
  const puzzleBooks = books.filter((book) => book.id !== 1);

  return (
    <section id="books" className="relative pt-32 pb-16 bg-white dark:bg-gray-dark overflow-hidden">
      {/* Top Decoration */}
      <div className="absolute top-0 left-0 right-0 h-[120px] pointer-events-none transform scale-x-[-1] scale-y-[-1]">
        <BooksDesignTop className="w-full h-full opacity-20" />
      </div>

      {/* Bottom Decoration */}
      <div className="absolute bottom-0 left-0 right-0 h-[120px] pointer-events-none">
        <BooksDesignBottom className="w-full h-full opacity-20" />
      </div>

      {/* Left Side Decoration */}
      <div className="absolute left-[40px] top-0 w-[100px] h-full pointer-events-none opacity-20">
        <BooksDesignLeft className="w-full h-full" />
      </div>

      {/* Right Side Decoration */}
      <div className="absolute right-0 top-1/2 -translate-y-1/2 w-[100px] h-[60vh] pointer-events-none opacity-20 md:block hidden">
        <BooksDesignRight className="w-full h-full" />
      </div>

      <div className="container relative"> {/* Removed z-10 */}
        {/* Grid for Puzzle Books */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {puzzleBooks.map((book) => (
            <BookCard
              key={book.id}
              book={book}
              // Removed onImageClick prop
            />
          ))}
        </div>
      </div>

      {/* Removed the modal previously handled here */}
    </section>
  );
};

export default Books;
