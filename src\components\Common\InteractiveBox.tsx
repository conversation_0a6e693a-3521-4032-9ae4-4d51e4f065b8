// src/components/Common/InteractiveBox.tsx
"use client";

import { useState, useRef, MouseEvent, ReactNode } from "react";

interface InteractiveBoxProps {
  children: ReactNode;
  enableSpotlight?: boolean; // Prop to control spotlight effect
  className?: string; // Allow passing additional classes
}

const InteractiveBox: React.FC<InteractiveBoxProps> = ({
  children,
  enableSpotlight = false, // Default to false
  className = "",
}) => {
  // --- State and Ref for hover effects ---
  const boxRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);

  // --- Event Handlers ---
  const handleMouseMove = (e: MouseEvent<HTMLDivElement>) => {
    if (!boxRef.current || !enableSpotlight || isFocused) return; // Only run if spotlight enabled
    const div = boxRef.current;
    const rect = div.getBoundingClientRect();
    setPosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });
  };

  const handleFocus = () => {
    if (!enableSpotlight) return; // Only run if spotlight enabled
    setIsFocused(true);
    setOpacity(1);
  };

  const handleBlur = () => {
    if (!enableSpotlight) return; // Only run if spotlight enabled
    setIsFocused(false);
    setOpacity(0);
  };

  const handleMouseEnter = () => {
    if (!enableSpotlight) return; // Only run if spotlight enabled
    setOpacity(1);
  };

  const handleMouseLeave = () => {
    if (!enableSpotlight) return; // Only run if spotlight enabled
    setOpacity(0);
  };

  // Combine base classes with any passed classes
  const combinedClassName = `group relative border border-tokyo-red/50 dark:border-tokyo-turquoise/50 rounded shadow-lg hover:border-tokyo-turquoise dark:hover:border-tokyo-turquoise hover:shadow-neon-intense transition-all duration-200 overflow-hidden ${className}`;

  return (
    <div
      ref={boxRef}
      onMouseMove={handleMouseMove}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={combinedClassName}
      tabIndex={enableSpotlight ? 0 : -1} // Make focusable only if spotlight is enabled
    >
      {/* Spotlight Effect Div - Conditionally Rendered */}
      {enableSpotlight && (
        <div
          className="pointer-events-none absolute -inset-px opacity-0 transition duration-300 group-hover:opacity-100 z-10"
          style={{
            opacity,
            background: `radial-gradient(600px circle at ${position.x}px ${position.y}px, rgba(3, 169, 144, 0.1), transparent 40%)`, // Turquoise glow
          }}
        />
      )}
      {/* Content - Added relative z-0 to ensure content is below spotlight */}
      <div className="relative z-0">{children}</div>
    </div>
  );
};

export default InteractiveBox;