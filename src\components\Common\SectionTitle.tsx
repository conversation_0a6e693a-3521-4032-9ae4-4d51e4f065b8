const SectionTitle = ({
  title,
  paragraph,
  width = "570px",
  center,
  mb = "100px",
  titleClass = "mb-4 text-3xl font-bold !leading-tight text-black dark:text-white sm:text-4xl md:text-[45px]",
  paragraphClass = "text-base !leading-relaxed text-body-color md:text-lg",
}: {
  title: string;
  paragraph: string;
  width?: string;
  center?: boolean;
  mb?: string;
  titleClass?: string;
  paragraphClass?: string;
}) => {
  return (
    <>
      <div
        className={`w-full ${center ? "mx-auto text-center" : ""}`}
        style={{ maxWidth: width, marginBottom: mb }}
      >
        <h2 className={titleClass}>
          {title}
        </h2>
        <p className={paragraphClass}>
          {paragraph}
        </p>
      </div>
    </>
  );
};

export default SectionTitle;
