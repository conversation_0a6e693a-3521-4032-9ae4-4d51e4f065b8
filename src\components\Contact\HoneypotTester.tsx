"use client";

import { useEffect, useState, MutableRefObject } from 'react';

interface HoneypotTesterProps {
  honeypotRef: MutableRefObject<HTMLInputElement | null>;
}

export default function HoneypotTester({ honeypotRef }: HoneypotTesterProps) {
  const [isClient, setIsClient] = useState(false);
  const [isLocalhost, setIsLocalhost] = useState(false);

  useEffect(() => {
    setIsClient(true);

    const hostname = window.location.hostname;
    if (hostname === 'localhost' || hostname.includes('127.0.0.1')) {
      setIsLocalhost(true);
      console.log('HoneypotTester: Localhost detected, showing testing UI');
    }
  }, []);

  // Only render on client and only on localhost
  if (!isClient || !isLocalhost) {
    return null;
  }

  return (
    <div className="w-full px-4 mb-8">
      <div style={{
        padding: '20px',
        border: '3px solid red',
        backgroundColor: '#fff0f0',
        borderRadius: '8px',
        marginBottom: '20px',
        color: '#9B1C1C' // Ensure text is dark red for better contrast
      }}>
        <h3 className="text-xl font-bold text-red-600 mb-2">🚨 HONEYPOT TESTING FIELD</h3>
        <p className="mb-4 text-red-800">This field is <strong>only visible in localhost</strong> for testing purposes.</p>
        <label className="block font-bold text-red-800 mb-2" htmlFor="honeypot-test">
          Your Favorite Color * (Honeypot field - fill to test bot detection)
        </label>
        <input
          type="text"
          id="honeypot-test"
          className="w-full p-2 border-2 border-red-300 rounded bg-white text-black"
          placeholder="Type anything here to trigger bot detection"
          onChange={(e) => {
            if (honeypotRef.current) {
              honeypotRef.current.value = e.target.value;
            }
          }}
        />
        <p className="mt-2 text-sm text-red-800">
          In production, this field is invisible to humans but visible to bots.
          The asterisk (*) makes bots think it&apos;s required.
        </p>
      </div>
    </div>
  );
}
