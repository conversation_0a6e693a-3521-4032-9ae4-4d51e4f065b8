'use client';

import React, { useState, useRef, useEffect } from "react";
import SubscribeBox from "../SubscribeBox";
import styles from "./honeypot.module.css";
import HoneypotTester from "./HoneypotTester";

const sendingMessage = "Sending message, please wait..."
const messageSentSuccess = "Message sent successfully! We'll get back to you soon."


declare global {
  interface Window {
    turnstile?: {
      render: (container: HTMLElement | string, options: {
        sitekey: string;
        callback?: (token: string) => void;
        'expired-callback'?: () => void;
        'error-callback'?: () => void;
      }) => string;
      reset: (container: HTMLElement | string) => void;
      remove: (widgetId: string) => void;
    };
    // Add global callback functions for Turnstile
    onTurnstileSuccess?: (token: string) => void;
    onTurnstileExpired?: () => void;
    onTurnstileError?: () => void;
  }
}

const Contact = () => {
  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [messageSent, setMessageSent] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [turnstileRenderError, setTurnstileRenderError] = useState<string | null>(null); // Added state for render errors
  const [botDetected, setBotDetected] = useState(false); // State to track if a bot was detected
  const [isDevMode, setIsDevMode] = useState(false); // Track if we're in development mode
  const turnstileRef = useRef<HTMLDivElement>(null);
  const widgetId = useRef<string | null>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const honeypotRef = useRef<HTMLInputElement>(null); // Honeypot field ref

  useEffect(() => {
    let isMounted = true;
    let timerId: NodeJS.Timeout | null = null;
    const turnstileRefCurrent = turnstileRef.current; // Capture ref value for cleanup

    const cleanup = () => {
      isMounted = false;
      if (timerId) {
        clearTimeout(timerId);
      }
      // Cleanup widget only if it was rendered and turnstile is available
      if (widgetId.current && window.turnstile) {
        try {
          console.log('Removing Turnstile widget:', widgetId.current);
          window.turnstile.remove(widgetId.current);
          widgetId.current = null; // Reset widget ID after removal
        } catch (error) {
          console.error('Error removing Turnstile widget during cleanup:', error);
        }
      } else {
         console.log('Cleanup: No Turnstile widget ID or window.turnstile not found, skipping removal.');
      }
    };

    const initTurnstile = () => {
      if (!isMounted || !window.turnstile || !turnstileRefCurrent) {
        console.log('Turnstile init skipped:', { isMounted, hasWindowTurnstile: !!window.turnstile, hasTurnstileRef: !!turnstileRefCurrent });
        return;
      }

      // Always ensure sitekey is a proper string from env vars
      const sitekey = String(process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || "");

      if (!sitekey) {
        console.error('Turnstile site key is missing.');
        // Set error state instead of direct DOM manipulation
        setTurnstileRenderError('Configuration error. Security check unavailable.');
        return;
      }

      if (!sitekey) {
        console.error('Turnstile site key is missing.');
        // Set error state instead of direct DOM manipulation
        setTurnstileRenderError('Configuration error. Security check unavailable.');
        return;
      }

      // Ensure the container is empty before rendering
      if (turnstileRefCurrent) {
         turnstileRefCurrent.innerHTML = '';
      }

      console.log('Attempting to render Turnstile widget...');
      try {
        // Check if a widget already exists in the container (e.g., due to HMR or race condition)
        // This check might be redundant if cleanup is robust, but adds safety.
        // Note: Removed check for window.turnstile?.getWidget as it's not in the type definition.
        // The cleanup function should handle removing any existing widget before mount.

        // Create a configuration object with all options properly typed
        const turnstileOptions = {
          sitekey: sitekey,
          callback: (token: string) => {
            if (!isMounted) return; // Avoid state updates on unmounted component
            console.log('Turnstile token received:', token);
            setTurnstileToken(token);
            setTurnstileRenderError(null); // Clear any previous render errors on success
          },
          'expired-callback': () => {
            if (!isMounted) return;
            console.log('Turnstile token expired');
            setTurnstileToken(null);
            // Optionally reset or show a message if needed upon expiration
          },
          'error-callback': () => {
            console.error('Turnstile widget failed to load or render.');
            if (isMounted) {
              // Set error state instead of direct DOM manipulation
              setTurnstileRenderError('Security check failed. Please refresh.');
            }
          }
        };

        widgetId.current = window.turnstile.render(turnstileRefCurrent, turnstileOptions);
        console.log('Turnstile widget rendered with ID:', widgetId.current);
      } catch (error) {
        console.error('Turnstile render error:', error);
        if (isMounted) {
          // Set error state instead of direct DOM manipulation
          setTurnstileRenderError('Error rendering security check. Please refresh.');
        }
      }
    };

    // Check if Turnstile is loaded. If not, wait a bit as it's loaded deferred.
    if (window.turnstile) {
      console.log('Turnstile already loaded, initializing.');
      initTurnstile();
    } else {
      console.log('Turnstile not immediately available, setting timeout.');
      timerId = setTimeout(() => {
        timerId = null; // Clear timerId after it runs
        if (isMounted) {
          if (window.turnstile) {
            console.log('Turnstile loaded after timeout, initializing.');
            initTurnstile();
          } else {
            console.error('Turnstile script did not load within timeout.');
            if (isMounted) {
              // Set error state instead of direct DOM manipulation
              setTurnstileRenderError('Security check timed out. Please refresh.');
            }
          }
        }
      }, 1500); // Increased wait time slightly
    }

    // Return the single cleanup function
    return cleanup;

  }, []); // Empty dependency array ensures this runs once on mount

  // Add useEffect to detect localhost for honeypot testing
  useEffect(() => {
    // This will only run in the browser after hydration
    const hostname = window.location.hostname;
    console.log('Contact: Current hostname:', hostname);

    if (hostname === 'localhost' || hostname.includes('127.0.0.1')) {
      console.log('Contact: Development mode detected - honeypot field will be visible for testing');
      setIsDevMode(true);
    }

    // Add a class to the body to indicate client-side rendering is complete
    document.body.classList.add('client-rendered');
  }, []);

  return (
    <section id="contact" className="bg-gray-light dark:bg-dark-alt overflow-hidden py-16 md:py-20 lg:py-28"> {/* Applied alternating background */}
      <div className="container">
        <div className="-mx-4 flex flex-wrap">
          <div className="w-full px-4 lg:w-7/12 xl:w-8/12">
            <div
              className="mb-12 rounded-lg bg-white dark:bg-gray-dark px-8 py-11 shadow-neon sm:p-[55px] lg:mb-5 lg:px-8 xl:p-[55px]" // Added light mode bg
              data-wow-delay=".15s"
            >
              <h2 className="mb-3 text-2xl font-bold text-tokyo-turquoise sm:text-3xl lg:text-2xl xl:text-3xl font-orbitron tracking-wider">
                Need Help? Open a Ticket
              </h2>
              <p className="mb-12 text-base font-medium text-body-color dark:text-body-color-dark font-orbitron tracking-wider"> {/* Added light mode text */}
                Our support team will get back to you ASAP via email.
              </p>
              <form ref={formRef} onSubmit={async (e) => {
                e.preventDefault();
                const formData = new FormData(e.currentTarget);
                formData.append('cf-turnstile-response', turnstileToken || '');

                // Get honeypot field value
                const favorite_color = honeypotRef.current?.value || '';
                formData.append('favorite_color', favorite_color);

                // Check if honeypot field is filled (bot detected)
                if (favorite_color) {
                  console.log('Bot detected! Honeypot field was filled.');
                  setBotDetected(true);
                  setSubmitError("We've detected that you're a bot. This form cannot be submitted.");
                  setIsSubmitting(false);
                  return;
                }

                setIsSubmitting(true);
                setMessageSent(false);
                setSubmitError(null);

                try {
                  const response = await fetch('/api/contact', {
                    method: 'POST',
                    body: formData
                  });

                  if (!response.ok) {
                    const errorData = await response.json();
                    console.error('API Error:', errorData);
                    throw new Error(errorData.error || 'Submission failed');
                  }

                  const result = await response.json();
                  console.log('API Success:', result);

                  setMessageSent(true);
                  // Reset form fields using ref instead of event
                  if (formRef.current) {
                    formRef.current.reset();
                  }
                  // Simplified Reset Turnstile
                  try {
                    if (window.turnstile && widgetId.current) {
                      console.log('Resetting Turnstile widget:', widgetId.current);
                      window.turnstile.reset(widgetId.current);
                      setTurnstileToken(null); // Clear the token state
                    } else if (window.turnstile && turnstileRef.current) {
                       // Fallback if widgetId is lost, try resetting container
                       console.log('Resetting Turnstile container (fallback)');
                       window.turnstile.reset(turnstileRef.current);
                       setTurnstileToken(null);
                    }
                  } catch (error) {
                    console.error('Error resetting Turnstile:', error);
                    // If reset fails, maybe set an error state or log, but avoid complex re-render logic here
                    setSubmitError("Failed to reset security check. Please refresh if needed.");
                  }
                } catch (error) {
                  console.error('Submission Error:', error);
                  setSubmitError(error instanceof Error ? error.message : 'Failed to send message');
                } finally {
                  setIsSubmitting(false);
                }
              }}>
                <div className="-mx-4 flex flex-wrap">
                  <div className="w-full px-4 md:w-1/2">
                    <div className="mb-8">
                      <label
                        htmlFor="name"
                        className="mb-3 block text-sm font-medium text-tokyo-turquoise font-orbitron tracking-wider"
                      >
                        Your Name
                      </label>
                      <input
                        type="text"
                        name="name"
                        placeholder="Enter your name"
                        required
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-dark-secondary px-6 py-3 text-base text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-500 outline-none focus:border-tokyo-turquoise focus:ring-2 focus:ring-tokyo-turquoise/50 font-rajdhani"
                      />
                    </div>
                  </div>
                  <div className="w-full px-4 md:w-1/2">
                    <div className="mb-8">
                      <label
                        htmlFor="email"
                        className="mb-3 block text-sm font-medium text-tokyo-turquoise font-orbitron tracking-wider"
                      >
                        Your Email
                      </label>
                      <input
                        type="email"
                        name="email"
                        placeholder="Enter your email"
                        required
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-dark-secondary px-6 py-3 text-base text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-500 outline-none focus:border-tokyo-turquoise focus:ring-2 focus:ring-tokyo-turquoise/50 font-rajdhani"
                      />
                    </div>
                  </div>
                  <div className="w-full px-4">
                    <div className="mb-8">
                      <label
                        htmlFor="message"
                        className="mb-3 block text-sm font-medium text-tokyo-turquoise font-orbitron tracking-wider"
                      >
                        Your Message
                      </label>
                      <textarea
                        name="message"
                        rows={5}
                        placeholder="Enter your Message"
                        required
                        className="w-full resize-none rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-dark-secondary px-6 py-3 text-base text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-500 outline-none focus:border-tokyo-turquoise focus:ring-2 focus:ring-tokyo-turquoise/50 font-rajdhani"
                      ></textarea>
                    </div>
                  </div>
                  {/* Hidden honeypot field (invisible in production) */}
                  <div className={styles.honeypot}>
                    <div className="mb-8">
                      <label
                        htmlFor="favorite_color"
                        className="mb-3 block text-sm font-medium text-dark dark:text-white"
                      >
                        Your Favorite Color *
                      </label>
                      <input
                        type="text"
                        id="favorite_color"
                        name="favorite_color"
                        ref={honeypotRef}
                        placeholder="Please enter your favorite color"
                        className="border-stroke w-full rounded-sm border bg-[#f8f8f8] px-6 py-3 text-base text-body-color outline-none focus:border-primary dark:border-transparent dark:bg-[#2C303B] dark:text-body-color-dark dark:shadow-two dark:focus:border-primary dark:focus:shadow-none"
                        tabIndex={-1}
                        aria-hidden="true"
                      />
                    </div>
                  </div>

                  {/* Client-side only honeypot tester component */}
                  <HoneypotTester honeypotRef={honeypotRef} />

                  <div className="w-full px-4">
                    <div className="mb-8">
                      {/* Turnstile container */}
                      <div ref={turnstileRef} className="cf-turnstile mb-4 self-center min-h-[65px]"> {/* Added min-height */}
                        {/* Conditionally render error message inside the container */}
                        {turnstileRenderError && (
                          <p className="text-red-500 text-sm">{turnstileRenderError}</p>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="w-full px-4">
                    <button
                      type="submit"
                      className="rounded-lg bg-tokyo-turquoise px-9 py-4 text-base font-bold text-black dark:text-white transition-all duration-300 hover:bg-tokyo-turquoise/90 hover:shadow-neon-intense font-orbitron"
                      disabled={!turnstileToken || isSubmitting || botDetected}
                    >
                      {isSubmitting ? 'Sending...' : 'Submit Ticket'}
                    </button>

                    <div className="mt-4 h-6">
                      {botDetected ? (
                        <p className="text-tokyo-pink font-bold">
                          We&apos;ve detected that you&apos;re a bot. This form cannot be submitted.
                        </p>
                      ) : isSubmitting ? (
                        <p className="text-tokyo-turquoise">{sendingMessage}</p>
                      ) : messageSent ? (
                        <p className="text-tokyo-turquoise">{messageSentSuccess}</p>
                      ) : submitError ? (
                        <p className="text-tokyo-pink">{submitError}</p>
                      ) : null}
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
          <div className="w-full px-4 lg:w-5/12 xl:w-4/12">
            <SubscribeBox />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;