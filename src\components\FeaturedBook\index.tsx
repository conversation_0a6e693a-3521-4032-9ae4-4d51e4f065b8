"use client";

import { useState, useRef, useEffect, MouseE<PERSON> } from "react";
import Image from "next/image"; // Keep for BookCard if needed, or remove if unused
import BookCard from "@/components/Books/BookCard";
import { books } from "@/data/books";
import ImageZoomGallery from "@/components/ImageZoomGallery"; // Import the new component
// Base thumbnail images
const baseThumbnailImages = [
  "/images/books/thumbs/002-fostering-love-for-learning-zvi-twersky.webp",
  "/images/books/thumbs/003-childrens-book-heart-anatomy-medical.webp",
  "/images/books/thumbs/004-fly-into-your-heart-with-the-cat-who-is-smart-childrens-book-heart-anatomy-medical.webp",
  "/images/books/thumbs/005-06-028_029-Fly-Into-Your-Heart.webp",
  "/images/books/thumbs/006-childrens-book-heart-anatomy.webp",
  "/images/books/thumbs/fly-into-your-heart-kids-hands.webp",
];

const FeaturedBook = () => {
  const featuredBook = books.find((book) => book.id === 1);

  // Combine base thumbnails with the main cover image
  const thumbnailImages = featuredBook?.imageUrl ? [featuredBook.imageUrl, ...baseThumbnailImages] : baseThumbnailImages;

  // State for the initial index for ImageZoomGallery (optional, defaults to 0)
  // const [initialImageIndex, setInitialImageIndex] = useState(0);

  // Refs and state for spotlight effect remain
  // State and Ref for main container hover effects
  const mainContainerRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);


  // Removed useEffect related to setting mainImage

  if (!featuredBook) {
    return null;
  }

  // Removed thumbnail hover/leave handlers
  // Removed thumbnail scroll handlers

  // --- Main Container Hover Effect Handlers ---
  const handleMouseMove = (e: MouseEvent<HTMLDivElement>) => {
    if (!mainContainerRef.current || isFocused) return;
    const div = mainContainerRef.current;
    const rect = div.getBoundingClientRect();
    setPosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });
  };

  const handleFocus = () => {
    setIsFocused(true);
    setOpacity(1);
  };

  const handleBlur = () => {
    setIsFocused(false);
    setOpacity(0);
  };

  const handleMouseEnter = () => {
    setOpacity(1);
  };

  const handleMouseLeave = () => {
    setOpacity(0);
  };


  
  // Prepare props for ImageZoomGallery
  const galleryImages = thumbnailImages; // Use the combined array
  const galleryAlts = [
    featuredBook.title, // Alt for the main cover
    ...baseThumbnailImages.map((_, i) => `Book interior view ${i + 1}`) // Generic alts for others
  ];

  return (
      <section id="featured-book" className="relative z-0 pb-16 md:pb-20 lg:pb-28 bg-white dark:bg-gray-dark "> {/* Removed top padding */}
        <div className="container">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="section-title text-4xl font-orbitron">
            <span className="text-tokyo-red red-text-glow">Books</span> & <span className="text-tokyo-turquoise text-glow">Puzzles</span>
          </h2>
          <p className="text-body-color dark:text-body-color-dark mx-auto text-2xl mt-4"> {/* Removed max-w-2xl */}
            Explore our collection of children&apos;s books and mind-engaging puzzles
          </p>
        </div>

        {/* Main Featured Book Box */}
        <div
          ref={mainContainerRef}
          onMouseMove={handleMouseMove}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          className="group relative -mt-8 border border-tokyo-red/50 dark:border-tokyo-turquoise/50 rounded-lg p-6 md:p-8 lg:p-10 shadow-lg hover:border-tokyo-turquoise dark:hover:border-tokyo-turquoise hover:shadow-neon-intense transition-all duration-200 overflow-hidden"
       >
          {/* Spotlight Effect Div */}
          <div
            className="pointer-events-none absolute -inset-px opacity-0 transition duration-300 group-hover:opacity-100 z-10" // Added z-10
            style={{
              opacity,
              background: `radial-gradient(600px circle at ${position.x}px ${position.y}px, rgba(3, 169, 144, 0.1), transparent 40%)`,
            }}
          />
          {/* Content Grid - Removed relative z-[1] */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">

            {/* Left Column: Image + Thumbnails */}
            <div className="flex flex-col items-start"> {/* Align items to the start (top) */}
              {/* Replace Main Image and Thumbnails with ImageZoomGallery */}
              <div className="w-full"> {/* Container for the gallery - Removed max-w-lg */}
                <ImageZoomGallery
                  images={galleryImages}
                  alts={galleryAlts}
                  initialIndex={0} // Start with the main cover image
                  // Pass down any necessary class names if needed
                  // mainImageClassName="rounded-md shadow-md"
                  // thumbnailClassName="w-16 h-16" // Example: Adjust thumbnail size if needed
                />
              </div>
            </div>

            {/* Right Column: Book Details */}
            <div className="flex items-start justify-center"> {/* Align items to the start (top) */}
               <BookCard
                 key={featuredBook.id}
                 book={featuredBook}
                 hideImage={true}
                 disableHoverEffects={true}
                 className="w-full max-w-md p-0 bg-transparent dark:bg-transparent border-none shadow-none"
               />
            </div>

          </div>
        </div>
      </div>

      {/* Removed the custom Zoom Modal section */}

      {/* CSS (keep relevant parts) */}
      <style jsx global>{`
        /* Removed .thumb-glow styles as they are handled internally or via props */
        /* Removed scrollbar hiding styles */
        /* max-h-[90vh] is used inside ImageZoomGallery now */
        .max-h-\[90vh\] { max-height: 90vh; }

        /* Define neon shadow color variable */
        :root {
          --neon-shadow-color-light: rgba(3, 169, 144, 0.4);
          --neon-shadow-color-dark: rgba(3, 169, 144, 0.5);
        }
        .dark {
           --neon-shadow-color-light: rgba(3, 169, 144, 0.5);
        }

        /* Apply neon shadow using the variable */
        .hover\\:shadow-neon-intense:hover {
           box-shadow: 0 0 20px 8px var(--neon-shadow-color-light);
        }
        .dark .hover\\:shadow-neon-intense:hover {
           box-shadow: 0 0 20px 8px var(--neon-shadow-color-dark);
        }
      `}</style>
    </section>
  );
};

export default FeaturedBook;