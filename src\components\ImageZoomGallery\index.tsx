"use client";
import React, { useState, useCallback, useEffect } from 'react';
import Image from 'next/image';
import Lightbox from "yet-another-react-lightbox"; // Import Lightbox
import "yet-another-react-lightbox/styles.css"; // Import Lightbox styles
export interface ImageZoomGalleryProps {
  images: string[]; // Array of image URLs
  alts: string[];   // Array of corresponding alt texts
  initialIndex?: number; // Optional index to show initially in the main display
  onThumbnailHover?: (index: number) => void; // Callback for thumbnail hover
  // Removed onMainImageClick prop as lightbox handles clicks internally
  thumbnailClassName?: string; // Optional class for thumbnails
  mainImageClassName?: string; // Optional class for the main image container
}

// This component will now handle both the thumbnail display logic
// and the zoomed modal gallery view.
const ImageZoomGallery: React.FC<ImageZoomGalleryProps> = ({
  images,
  alts,
  initialIndex = 0,
  onThumbnailHover = () => {},
  // Removed onMainImageClick default prop
  thumbnailClassName = "",
  mainImageClassName = ""
}) => {
  const [currentIndex, setCurrentIndex] = useState<number>(initialIndex); // For the main displayed image
  // State for Lightbox
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);

  // Update current index if initialIndex prop changes
  useEffect(() => {
    setCurrentIndex(initialIndex);
  }, [initialIndex]);

  // Removed custom modal keyboard navigation and body scroll effects

  // Open Lightbox
  const openLightbox = (index: number) => {
    setLightboxIndex(index);
    setLightboxOpen(true);
  };

  // Handle thumbnail hover
  const handleThumbHover = (index: number) => {
    setCurrentIndex(index);
    onThumbnailHover(index); // Trigger the passed callback
  };

  // Ensure we have images to display
  if (!images || images.length === 0) {
    return <div>No images provided.</div>;
  }

  // Custom slide renderer for Lightbox to handle image click (similar to ai-edits)
  const renderSlide = ({ slide, rect }) => {
    // Estimate dimensions if not provided, or use defaults
    const width = slide.width || 1000; // Default width if not provided
    const height = slide.height || 1000; // Default height if not provided
    const fit = "contain"; // Always contain for this gallery
    const slideWidth = Math.round(Math.min(rect.width, (rect.height / height) * width));
    const slideHeight = Math.round(Math.min(rect.height, (rect.width / width) * height));

    return (
      <Image
        fill={false} // Use explicit width/height for better layout control
        src={slide.src}
        alt={slide.alt}
        loading="eager"
        draggable={false}
        width={slideWidth}
        height={slideHeight}
        style={{
            objectFit: fit,
            cursor: 'pointer',
            width: slideWidth,
            height: slideHeight,
            margin: '0 auto' // Center the image horizontally
        }}
        sizes={`${Math.ceil((slideWidth / window.innerWidth) * 100)}vw`}
        onClick={() => {
          // Go to the next slide, wrapping around
          const nextIndex = (lightboxIndex + 1) % images.length;
          setLightboxIndex(nextIndex);
          // Allow backdrop click to close
        }}
      />
    );
  };

  return (
    <>
      {/* Main Image Display - Fixed Height Container (like original ImageZoom) */}
      {/* Apply a fixed height (e.g., h-[500px]) and make image fill and contain */}
      <div className={`relative w-full h-96 overflow-hidden ${mainImageClassName}`}> {/* Added h-96 like original ImageZoom */}
        <Image
          key={images[currentIndex]} // Add key to help React with transitions if src changes
          src={images[currentIndex]}
          alt={alts[currentIndex] || `Image ${currentIndex + 1}`}
          width={500} // Provide reasonable width hint
          height={500} // Provide reasonable height hint (aspect ratio doesn't strictly matter with object-contain)
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 500px" // Adjust sizes based on container
          // Fill container and use object-contain to scale image within fixed height
          className="w-full h-full object-contain cursor-pointer" // Changed cursor
          priority={true} // Prioritize loading the main image
          onClick={() => openLightbox(currentIndex)} // Open lightbox
        />
      </div>

      {/* Thumbnails Display - Single scrollable row */}
      {images.length > 1 && (
        <div className="flex flex-nowrap overflow-x-auto space-x-2 pb-2 scroll-smooth snap-x snap-mandatory md:justify-center md:space-x-3 mt-[10px]"> {/* Added mt-[10px] for 10px top margin */}
          {images.map((src, index) => (
            <div // Added flex-shrink-0 and snap-center
              key={index}
              className={`relative flex-shrink-0 snap-center overflow-hidden border-2 cursor-pointer rounded ${currentIndex === index ? 'border-blue-500 ring-2 ring-offset-1 ring-blue-400' : 'border-transparent'} ${thumbnailClassName}`} // Added flex-shrink-0, snap-center, rounded
              style={{ width: '60px', height: '60px' }} // Adjusted size to be closer to original screenshot
              onMouseEnter={() => handleThumbHover(index)}
              onClick={() => openLightbox(index)} // Click thumbnail to open lightbox at that index
            >
              <Image
                src={src}
                alt={alts[index] || `Thumbnail ${index + 1}`}
                fill // Use fill to cover the container
                sizes="80px"
                className="object-cover"
                loading={index < 5 ? "eager" : "lazy"} // Eager load first few thumbs
              />
            </div>
          ))}
        </div>
      )}

      {/* Lightbox component */}
      <Lightbox
        open={lightboxOpen}
        close={() => setLightboxOpen(false)}
        index={lightboxIndex}
        slides={images.map((src, i) => ({
          src: src,
          alt: alts[i] || `Image ${i + 1}`,
          // You might need to provide width/height if known for better performance/layout
          // width: 1000,
          // height: 1000,
        }))}
        on={{
          view: ({ index: currentIndex }) => setLightboxIndex(currentIndex),
        }}
        render={{ slide: renderSlide }} // Use custom slide renderer for image clicks
        styles={{ container: { backgroundColor: "rgba(0, 0, 0, .9)" } }} // Darker background like custom modal
      />
    </>
  );
};

export default ImageZoomGallery;