"use client";
import React, { useState, useEffect } from 'react'; // Added useEffect
import { createPortal } from 'react-dom'; // Added createPortal
import Image from 'next/image';

interface ImageZoomProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  className?: string;
  caption?: string;
}

const ImageZoom: React.FC<ImageZoomProps> = ({
  src,
  alt,
  width,
  height,
  className,
  caption
}) => {
  const [zoomed, setZoomed] = useState(false);
  const [isClient, setIsClient] = useState(false); // State to track client-side mount

  // Set isClient to true only after component mounts
  useEffect(() => {
    setIsClient(true);
  }, []);

  const toggleZoom = () => {
    setZoomed(!zoomed);
  };

  // Modal JSX extracted for clarity
  const modalContent = (
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-75" // Increased z-index
      onClick={toggleZoom} // Close on backdrop click
    >
      {/* Container for image and button */}
      <div
        className="max-w-full max-h-full relative" // Removed flex centering and w/h-full
        onClick={(e) => e.stopPropagation()} // Prevent close on image container click
      >
        {/* Zoomed Image - Added onClick */}
        <Image
          src={src}
          alt={alt}
          width={5000} // Large width for potentially large source
          height={3000} // Large height
          className="w-auto h-auto max-w-full max-h-[90vh] object-contain cursor-zoom-out" // Constrained display, added cursor
          unoptimized={true}
          priority={true}
          onClick={toggleZoom} // Close on image click
        />
        {/* Close Button */}
        <button
          className="absolute top-4 right-4 w-12 h-12 rounded-full bg-black bg-opacity-50 text-white flex items-center justify-center cursor-pointer hover:bg-opacity-75"
          onClick={(e) => {
            e.stopPropagation();
            toggleZoom();
          }}
        >
          <span className="text-3xl">&times;</span>
        </button>
      </div>
    </div>
  );

  return (
    <>
      {/* Outer container - Ignore pointer events by default */}
      <div className={`my-4 w-full pointer-events-none ${className || ''}`}>
        {/* Clickable image container - Allow pointer events here */}
        <div
          className={`relative pointer-events-auto h-96 ${zoomed ? 'cursor-zoom-out' : 'cursor-zoom-in'}`} // Added fixed height h-96
          onClick={toggleZoom}
        >
          <Image
            src={src}
            alt={alt}
            width={width}
            height={height}
            className="w-full h-full object-contain" // Fill container and contain image
          />
        </div>
        {/* Optional caption */}
        {caption && (
          <div className="text-center text-sm mt-2 italic">
            {caption.startsWith('http://') || caption.startsWith('https://') ? (
              <a
                href={caption}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:underline"
                onClick={(e) => e.stopPropagation()}
              >
                {caption}
              </a>
            ) : (
              caption
            )}
          </div>
        )}
      </div>

      {/* Render modal using Portal only on client-side when zoomed */}
      {isClient && zoomed && document.getElementById('modal-root') ?
        createPortal(modalContent, document.getElementById('modal-root') as HTMLElement) :
        null
      }
    </>
  );
};

export default ImageZoom;