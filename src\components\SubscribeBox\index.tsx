"use client";

import { useTheme } from "next-themes";
import React, { useState, useRef, useEffect } from "react";


const sendingMessage = "Subscribing, please wait...";
const messageSentSuccess = "Successfully subscribed! Welcome aboard.";

declare global {
  interface Window {
    turnstile?: {
      render: (container: HTMLElement | string, options: {
        sitekey: string;
        callback?: (token: string) => void;
        'expired-callback'?: () => void;
        'error-callback'?: () => void;
      }) => string;
      reset: (container: HTMLElement | string) => void;
      remove: (widgetId: string) => void;
    };
    // Add global callback functions for Turnstile
    onTurnstileSuccess?: (token: string) => void;
    onTurnstileExpired?: () => void;
    onTurnstileError?: () => void;
  }
}

const SubscribeBox = () => {
  const { theme } = useTheme();
  const [email, setEmail] = useState("");

  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [messageSent, setMessageSent] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const turnstileRef = useRef<HTMLDivElement>(null);
  const widgetId = useRef<string | null>(null);
  const formRef = useRef<HTMLFormElement>(null);


  useEffect(() => {
    let isMounted = true;
    let timerId: NodeJS.Timeout | null = null;
    const turnstileRefCurrent = turnstileRef.current; // Capture ref value for cleanup

    const cleanup = () => {
      isMounted = false;
      if (timerId) {
        clearTimeout(timerId);
      }
      // Cleanup widget only if it was rendered and turnstile is available
      if (widgetId.current && window.turnstile) {
        try {
          console.log('Removing Turnstile widget (SubscribeBox):', widgetId.current);
          window.turnstile.remove(widgetId.current);
          widgetId.current = null; // Reset widget ID after removal
        } catch (error) {
          console.error('Error removing Turnstile widget during cleanup (SubscribeBox):', error);
        }
      } else {
         console.log('Cleanup (SubscribeBox): No Turnstile widget ID or window.turnstile not found, skipping removal.');
      }
    };

    const initTurnstile = () => {
      if (!isMounted || !window.turnstile || !turnstileRefCurrent) {
        console.log('Turnstile init skipped (SubscribeBox):', { isMounted, hasWindowTurnstile: !!window.turnstile, hasTurnstileRef: !!turnstileRefCurrent });
        return;
      }

      // Always ensure sitekey is a proper string from env vars
      const sitekey = String(process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || "");
      
      if (!sitekey) {
        console.error('Turnstile site key is missing (SubscribeBox).');
        if (turnstileRefCurrent) {
          turnstileRefCurrent.innerHTML = '<p class="text-red-500">Configuration error. Security check unavailable.</p>';
        }
        return;
      }

      // Ensure the container is empty before rendering
      if (turnstileRefCurrent) {
         turnstileRefCurrent.innerHTML = '';
      }

      console.log('Attempting to render Turnstile widget (SubscribeBox)...');
      try {
        // Create a configuration object with all options properly typed
        const turnstileOptions = {
          sitekey: sitekey,
          callback: (token: string) => {
            if (!isMounted) return; // Avoid state updates on unmounted component
            console.log('Turnstile token received (SubscribeBox):', token);
            setTurnstileToken(token);
          },
          'expired-callback': () => {
            if (!isMounted) return;
            console.log('Turnstile token expired (SubscribeBox)');
            setTurnstileToken(null);
          },
          'error-callback': () => {
            console.error('Turnstile widget failed to load or render (SubscribeBox).'); // Generic error message
            if (isMounted && turnstileRefCurrent) {
              turnstileRefCurrent.innerHTML = '<p class="text-red-500">Security check failed. Please refresh.</p>';
            }
          }
        };
        
        widgetId.current = window.turnstile.render(turnstileRefCurrent, turnstileOptions);
        console.log('Turnstile widget rendered with ID (SubscribeBox):', widgetId.current);
      } catch (error) {
        console.error('Turnstile render error (SubscribeBox):', error);
        if (isMounted && turnstileRefCurrent) {
          turnstileRefCurrent.innerHTML = '<p class="text-red-500">Error rendering security check. Please refresh.</p>';
        }
      }
    };

    // Check if Turnstile is loaded. If not, wait a bit as it's loaded deferred.
    if (window.turnstile) {
      console.log('Turnstile already loaded, initializing (SubscribeBox).');
      initTurnstile();
    } else {
      console.log('Turnstile not immediately available, setting timeout (SubscribeBox).');
      timerId = setTimeout(() => {
        timerId = null; // Clear timerId after it runs
        if (isMounted) {
          if (window.turnstile) {
            console.log('Turnstile loaded after timeout, initializing (SubscribeBox).');
            initTurnstile();
          } else {
            console.error('Turnstile script did not load within timeout (SubscribeBox).');
            if (turnstileRefCurrent) {
              turnstileRefCurrent.innerHTML = '<p class="text-red-500">Security check timed out. Please refresh.</p>';
            }
          }
        }
      }, 1500); // Increased wait time slightly
    }

    // Return the single cleanup function
    return cleanup;

  }, []); // Empty dependency array ensures this runs once on mount

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!turnstileToken) {
      setSubmitError("Please complete the security check.");
      return;
    }

    setIsSubmitting(true);
    setMessageSent(false);
    setSubmitError(null);

    const formData = new FormData();
    formData.append('name', 'Subscriber'); // Use a default name
    formData.append('email', email);
    formData.append('message', `New subscription request from: ${email}`); // Add context
    formData.append('cf-turnstile-response', turnstileToken);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API Error (SubscribeBox):', errorData);
        throw new Error(errorData.error || 'Subscription failed');
      }

      const result = await response.json();
      console.log('API Success (SubscribeBox):', result);

      setMessageSent(true);
      setEmail(""); // Clear email field on success

      // Reset Turnstile safely
      try {
        if (window.turnstile && widgetId.current) {
          window.turnstile.reset(widgetId.current);
          setTurnstileToken(null);
        }
      } catch (error) {
        console.error('Error resetting Turnstile (SubscribeBox):', error);
        // Optional: Add fallback logic to re-render if reset fails, similar to Contact component
        if (turnstileRef.current) {
          turnstileRef.current.innerHTML = ''; // Clear the container
          widgetId.current = null; // Reset widget ID
          // Re-initialize Turnstile after a short delay
          setTimeout(() => {
            if (window.turnstile && turnstileRef.current) {
              const sitekey = String(process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || "");
              const turnstileOptions = {
                sitekey: sitekey,
                callback: (token: string) => setTurnstileToken(token),
                'expired-callback': () => setTurnstileToken(null),
                'error-callback': () => {
                  if (turnstileRef.current) {
                    turnstileRef.current.innerHTML = '<p class=\"text-red-500\">Security check failed. Please refresh.</p>';
                  }
                }
              };
              widgetId.current = window.turnstile.render(turnstileRef.current, turnstileOptions);
            }
          }, 100);
        }
      }

    } catch (error) {
      console.error('Submission Error (SubscribeBox):', error);
      setSubmitError(error instanceof Error ? error.message : 'Failed to subscribe');
    } finally {
      setIsSubmitting(false);
    }
  };
  const frmDescription = "Join the Reader's Circle, and never miss a page-turning moment!";
  return (
    <div className="wow fadeInUp relative rounded-lg bg-white dark:bg-gray-dark p-8 shadow-neon sm:p-11 lg:p-8 xl:p-11" data-wow-delay=".2s"> {/* Removed z-10 */}
      <h3 className="mb-6 text-3xl font-orbitron font-bold text-black dark:text-black dark:text-white red-text-glow"> {/* Added light mode text */}
        Stay Updated!
      </h3>
      <p className="mb-8 border-b border-body-color border-opacity-25 pb-8 text-base leading-relaxed text-body-color dark:text-body-color-dark font-rajdhani"> {/* Added light mode text */}
        {frmDescription}
      </p>
      <form ref={formRef} onSubmit={handleSubmit}>
        <div className="flex flex-col space-y-4">
          <input
            type="email"
            name="email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-dark-secondary px-6 py-3 text-base text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-500 outline-none focus:border-tokyo-turquoise focus:ring-2 focus:ring-tokyo-turquoise/50 font-rajdhani"
            required
          />
          <div
            ref={turnstileRef}
            className="cf-turnstile mb-4 self-center"
          ></div>
          <button
            type="submit"
            className="w-full rounded-lg bg-tokyo-turquoise px-8 py-3 text-base font-bold text-black dark:text-white transition-all duration-300 hover:bg-tokyo-turquoise/90 hover:shadow-neon-intense font-orbitron"
          >
            {isSubmitting ? 'Subscribing...' : 'Subscribe'}
          </button>
        <div className="mt-4 h-6 text-center">
          {isSubmitting ? (
            <p className="text-blue-400">{sendingMessage}</p>
          ) : messageSent ? (
            <p className="text-green-400">{messageSentSuccess}</p>
          ) : submitError ? (
            <p className="text-red-400">{submitError}</p>
          ) : null}
        </div>
        </div>
        <p className="mt-4 text-center text-sm text-gray-400 font-rajdhani">
          No spam guaranteed. We respect your privacy.
        </p>
      </form>
    </div>
  );
};

export default SubscribeBox;