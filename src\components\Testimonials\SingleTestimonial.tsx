import { Testimonial } from "@/types/testimonial";
import Image from "next/image";
import React from "react";
const starIcon = (
  <svg width="18" height="16" viewBox="0 0 18 16" className="fill-current">
    <path d="M9.09815 0.361679L11.1054 6.06601H17.601L12.3459 9.59149L14.3532 15.2958L9.09815 11.7703L3.84309 15.2958L5.85035 9.59149L0.595291 6.06601H7.0909L9.09815 0.361679Z" />
  </svg>
);

const SingleTestimonial = ({ testimonial }: { testimonial: Testimonial }) => {
  const { star, name, image, content, designation, title } = testimonial; // Added title, removed date and product

  let ratingIcons: React.ReactElement[] = [];
  for (let index = 0; index < star; index++) {
    ratingIcons.push(
      <span key={index} className="text-amazon-orange">
        {starIcon}
      </span>,
    );
  }

  return (
    <div className="w-full h-full"> {/* Added h-full */}
      <div className="rounded-sm bg-white border border-gray-200 dark:bg-gray-dark-secondary dark:border-gray-700 p-8 shadow-neon duration-300 hover:shadow-neon-intense lg:px-5 xl:px-8 h-full flex flex-col"> {/* Added light mode bg/border, removed dark:bg-dark */}
        <div className="mb-3 flex items-center space-x-1">{ratingIcons}</div>
        <h4 className="mb-2 text-md font-semibold text-black dark:text-white font-rajdhani"> {/* Added title display */}
          {title}
        </h4>
        <p className="mb-8 border-b border-red-700 border-opacity-30 pb-8 text-base leading-relaxed text-body-color dark:text-gray-200 font-rajdhani flex-grow"> {/* Added light mode text */}
          &quot;{content}&quot;
        </p>
        <div className="flex items-center">
          <div className="w-full">
            <h3 className="mb-1 text-lg font-semibold text-black dark:text-white font-rajdhani"> {/* Added light mode text */}
              {name}
            </h3>
            <p className="text-sm text-primary dark:text-red-400 font-rajdhani">{designation}</p> {/* Changed light mode text to primary */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SingleTestimonial;
