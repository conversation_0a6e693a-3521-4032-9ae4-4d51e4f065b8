"use client";

import dynamic from "next/dynamic";
import { Suspense } from "react";

const ReactPlayer = dynamic(
  () => import("react-player"),
  {
    ssr: false,
    loading: () => <div className="w-full aspect-video bg-gray-200 animate-pulse" />
  }
);

interface VideoProps {
  videoId: string;
  title?: string;
  className?: string;
}

export function Video({ videoId, title, className }: VideoProps) {
  return (
    <div className={`${className} mx-auto max-w-4xl w-full px-4`}>
      <Suspense fallback={<div className="mx-auto max-w-4xl w-full px-4 aspect-video bg-gray-200 animate-pulse" />}>
        <ReactPlayer
          url={`https://vimeo.com/${videoId}`}
          width="100%"
          height="100%"
          controls
          config={{
            vimeo: {
              playerOptions: {
                title: title || false,
                responsive: true,
              },
            },
          }}
        />
      </Suspense>
    </div>
  );
}