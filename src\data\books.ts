// Define book type interface
export interface BookType {
  id: number;
  title: string;
  description: string;
  imageUrl: string;
  amazonLink: string;
  price: string;
  rating: number;
  reviews: number;
  pageCount: number; // Added pageCount
}

// Book data from crayolex.com
export const books: BookType[] = [
  // 1. Original Book 1 (Keep as is - B0CKMHSMG6)
  {
    id: 1,
    title: "Fly Into Your Heart With the Cat Who Is Smart",
    description: "Join a smart cat and a girl on a fun, whimsical rhyming adventure as they ride a magic sled through a huge human heart, learning its secrets in an entertaining way!",
    imageUrl: "/images/books/001-Fly-Into-Your-Heart.webp",
    amazonLink: "https://www.amazon.com/dp/B0CKMHSMG6",
    price: "$10.59",
    rating: 5.0,
    reviews: 11,
    pageCount: 48
  },
  // 2. Original Book 2 (Keep as is - B09SP1FRVK)
  {
    id: 2,
    title: "Medical Crosswords",
    description: "Challenge yourself with medical terminology in an enjoyable way, perfect for doctors, nurses, EMS personnel, and medical students!",
    imageUrl: "/images/books/007-MEDICAL-CROSSWORDS-PAPERBACK-FRONT.webp",
    amazonLink: "https://www.amazon.com/dp/B09SP1FRVK",
    price: "$10.59",
    rating: 4.2,
    reviews: 93,
    pageCount: 86
  },
  // 3. Original Book 7 (B0B86H2BPP)
  {
    id: 3,
    title: "Medical Crosswords", // Kids version
    description: "Introduce medical terminology through fun and educational crossword puzzles, great for aspiring EMTs and curious kids alike.",
    imageUrl: "/images/books/008-MEDICAL-CROSSWORDS-KIDS-PAPERBACK-FRONT.webp",
    amazonLink: "https://www.amazon.com/dp/B0B86H2BPP",
    price: "$9.99",
    rating: 5.0,
    reviews: 10,
    pageCount: 200
  },
  // 4. Original Book 6 (B0BZF7M4TM)
  {
    id: 4,
    title: "Crossing the Decades to 1950",
    description: "Travel back in time with these 1950s-themed word search puzzles, featuring cultural references from the fabulous fifties.",
    imageUrl: "/images/books/009-Crossing-the-Decades-to-1950-PAPERBACK-FRONT-RGB.webp",
    amazonLink: "https://www.amazon.com/dp/B0BZF7M4TM",
    price: "$9.99",
    rating: 5.0,
    reviews: 2,
    pageCount: 194
  },
  // 5. Original Book 4 (B0BBXT2T2G)
  {
    id: 5,
    title: "Medical Word Search",
    description: "Find over 1000 medical terms and uncover hidden messages in these word search puzzles.",
    imageUrl: "/images/books/010-MEDICAL-WORD-SEARCH-PAPERBACK-FRONT.webp",
    amazonLink: "https://www.amazon.com/dp/B0BBXT2T2G",
    price: "$9.99",
    rating: 4.9,
    reviews: 13,
    pageCount: 150
  },
  // 6. Original Book 5 (B0BGKL7P19)
  {
    id: 6,
    title: "Enlightening Word Search",
    description: "Discover inspirational and spiritual terms with these 80 enlightening word search puzzles.",
    imageUrl: "/images/books/011-ENLIGHTENING-WORD-SEARCH-PAPERBACK-FRONT.webp",
    amazonLink: "https://www.amazon.com/dp/B0BGKL7P19",
    price: "$9.99",
    rating: 4.7,
    reviews: 10,
    pageCount: 108
  },
  // 7. Original Book 3 (B0BJ7Y254P)
  {
    id: 7,
    title: "Christmas Word Search",
    description: "Get into the holiday spirit with 80 Christmas-themed word search puzzles.",
    imageUrl: "/images/books/012-CHRISTMAS-WORD-SEARCH-PAPERBACK-FRONT-RGB.webp",
    amazonLink: "https://www.amazon.com/dp/B0BJ7Y254P",
    price: "$9.99",
    rating: 4.4,
    reviews: 10,
    pageCount: 114
  }
];
