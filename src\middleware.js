import { NextResponse } from 'next/server';

// Files that should have noindex headers
const NOINDEX_PATHS = [
  // Specific SVG files mentioned in the request
  '/images/logo/crayolex-logo-white.svg',
  '/images/logo/crayolex-logo-dark.svg',
  
  // Common asset types that don't need to be indexed
  '/images/brands/',
  '/images/logo/',
  '/fonts/',
  '.svg',
  '.woff',
  '.woff2',
  '.ttf',
  '.eot',
  '.json',
  '.xml',
  '.ico',
];

export function middleware(request) {
  const { pathname } = request.nextUrl;
  
  // Check if the current path matches any of the noindex paths
  const shouldNoIndex = NOINDEX_PATHS.some(path => 
    pathname.includes(path) || pathname.endsWith(path)
  );
  
  // If it should be noindexed, add the appropriate headers
  if (shouldNoIndex) {
    const response = NextResponse.next();
    
    // Add X-Robots-Tag header to prevent indexing
    response.headers.set('X-Robots-Tag', 'noindex');
    
    return response;
  }
  
  // Otherwise, continue with the request as normal
  return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    // Match all paths that start with /images/ or /fonts/
    '/images/:path*',
    '/fonts/:path*',
    // Match all SVG files
    '/(.*).svg',
    // Match other asset types
    '/(.*).woff',
    '/(.*).woff2',
    '/(.*).ttf',
    '/(.*).eot',
    '/(.*).json',
    '/(.*).xml',
    '/(.*).ico',
  ],
};
