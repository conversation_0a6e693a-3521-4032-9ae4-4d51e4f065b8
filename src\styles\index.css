@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    /* Base font is now set via Tailwind config (theme.extend.fontFamily.sans) */
  }
}

@layer components {
  input[type="checkbox"]:checked ~ label span svg {
    @apply inline-flex;
  }

  .sticky .header-logo {
    @apply py-5 lg:py-2;
  }

  .sticky .menu-scroll.active {
    @apply opacity-70;
  }

  input#togglePlan:checked ~ .dot {
    @apply translate-x-full;
  }

  input#checkboxLabel:checked ~ .box span {
    @apply opacity-100;
  }

  .book-card {
    @apply relative overflow-hidden rounded-lg bg-white dark:bg-dark-alt shadow-lg transition-all duration-300; /* Changed dark bg */
  }

  .book-image {
    @apply transition-transform duration-500 ease-in-out;
  }

  .book-title {
    @apply font-bold text-black dark:text-white text-lg md:text-xl mb-2 leading-normal; /* Removed redundant font-orbitron, added leading-normal */
  }

  .book-price {
    @apply font-bold text-white text-lg;
  }

  .amazon-btn {
    @apply inline-flex items-center justify-center rounded-md bg-amazon-orange px-8 py-3 text-base font-semibold text-white transition-all duration-300 hover:bg-amazon-orange/90 hover:shadow-lg;
  }

  .amazon-btn:hover {
    @apply shadow-[0_0_15px_rgba(217,108,61,0.6)];
  }


  .neon-glow {
    @apply relative;
  }

  .neon-glow::before {
    content: '';
    @apply absolute inset-0 rounded-lg bg-gradient-to-r from-tokyo-red to-tokyo-turquoise opacity-0 transition-opacity duration-500 blur-lg -z-10;
  }

  .text-glow {
    text-shadow: 0 0 10px rgba(0, 255, 204, 0.7);
  }

  .red-text-glow {
    text-shadow: 0 0 10px rgba(255, 59, 92, 0.7);
  }

  .hero-title {
    @apply font-orbitron font-bold;
  }

  .section-title {
    @apply font-orbitron font-bold;
  }

  .font-orbitron {
    font-family: 'Orbitron', sans-serif;
  }

  .font-rajdhani {
    font-family: 'Rajdhani', sans-serif;
  }

  /* Testimonial styles */
  .shadow-neon {
    box-shadow: 0 0 10px rgba(0, 255, 204, 0.2);
  }

  .shadow-neon-intense {
    box-shadow: 0 0 20px rgba(0, 255, 204, 0.4), 0 0 10px rgba(255, 59, 92, 0.2);
  }

  .dark-theme-svg path {
    @apply transition-all duration-300;
  }

  .dark-theme-svg:hover path {
    @apply opacity-90;
  }

  .testimonial-content {
    position: relative;
  }

  .testimonial-content::before {
    content: "\201C";
    @apply absolute text-7xl opacity-20 text-tokyo-turquoise -left-2 -top-5 font-bold;
  }

  .testimonial-content::after {
    content: "\201D";
    @apply absolute text-7xl opacity-20 text-tokyo-red bottom-0 right-0 font-bold;
  }
}


/* Neon Border Button Animation */
.neon-border-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: conic-gradient(from var(--angle), transparent 20%, #00FFCC, transparent 80%); /* Reverted to brighter green */
  border-radius: inherit; /* Use the button's border-radius */
  z-index: -1;
  animation: rotateNeonBorder 4s linear infinite;
  --angle: 0deg; /* Custom property for animation */
  filter: drop-shadow(0 0 3px rgba(0, 255, 204, 0.5)); /* Add glow to animation in light mode */
}

@property --angle {
  syntax: '<angle>';
  initial-value: 0deg;
  inherits: false;
}

@keyframes rotateNeonBorder {
  0% { --angle: 0deg; }
  100% { --angle: 360deg; }
}

/* Optional: Add a subtle glow to the button itself */
.neon-border-button {
  box-shadow: 0 0 5px rgba(0, 255, 204, 0.3); /* Reverted to brighter green glow */
}
